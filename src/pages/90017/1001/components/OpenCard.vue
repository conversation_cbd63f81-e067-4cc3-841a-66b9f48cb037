<template>
  <div class="rule-bk">
    <div class="content">
      <div class="btn" @click="openCard"></div>
    </div>
    <div class="close" @click="close" />
  </div>
</template>

<script lang="ts" setup>
import { inject } from 'vue';
import type { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;
const emits = defineEmits(['close']);

const openCard = async () => {
  window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`;
};

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  border-radius: 0.2rem 0.2rem 0 0;
  width: 6.5rem;
  height: 8rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/263366/23/29462/22639/67ca89c8F89be0d74/e056f86178315fa8.png);
  background-size: 100%;
  background-repeat: no-repeat;
  }

  .close {
    width: 0.8rem;
    height: 0.8rem;
    margin: 0 auto;
    //background-color: #fff;
  }

  .content {
    height: 6.8rem;
    width: 90%;
    border: 0.3rem solid transparent;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #000;
    white-space: pre-wrap;
    padding: 1rem 0 0 0;
    margin: 0 auto 0.2rem;
    .title{
      display: flex;
      justify-content: space-between;
      padding: 0.2rem 0;
      color: #000;
      div{
        width: 50%;
        text-align: center;
      }
    }
    .btn {
      margin: 3.2rem auto 0;
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/253286/16/27472/15199/67ca89c7F2ec358af/a81ba1dcf662bc92.png);
      background-size: 100%;
      background-repeat: no-repeat;
      width: 3.71rem;
      height: 1.09rem;
    }
  }
</style>
