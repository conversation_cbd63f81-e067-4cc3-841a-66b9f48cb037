<template>
  <div class="count-down-time" :style="furnishStyles.cutDownColor.value">
    <span v-if="props.isStart" :style="furnishStyles.cutDownColor.value">距离活动结束剩余：</span>
    <span v-else :style="furnishStyles.cutDownColor.value">距离活动开始还有：</span>
    <van-count-down :time="props.isStart ? (props.endTime - new Date().getTime()) : (props.startTime - new Date().getTime())" format="DD:HH:mm:ss">
      <template #default="timeData">
        <div class="contentSpan">
          <div class="acblockStyleStyle">{{ timeData.days }}</div><span :style="furnishStyles.cutDownColor.value">天</span>
          <div class="acblockStyleStyle">{{ timeData.hours }}</div><span :style="furnishStyles.cutDownColor.value">时</span>
          <div class="acblockStyleStyle">{{ timeData.minutes }}</div><span :style="furnishStyles.cutDownColor.value">分</span>
          <div class="acblockStyleStyle">{{ timeData.seconds }}</div><span :style="furnishStyles.cutDownColor.value">秒</span>
        </div>
      </template>
    </van-count-down>
  </div>
</template>

<script setup lang="ts">
import furnishStyles, { furnish } from '../ts/furnishStyles';

const props = defineProps({
  isStart: {
    type: Boolean,
    default: false,
    required: true,
  },
  startTime: {
    type: Number,
    default: 0,
    required: true,
  },
  endTime: {
    type: Number,
    default: 0,
    required: true,
  },
});
</script>

<style scoped lang="scss">
.count-down-time {
  position: relative;
  top: 1.04rem;
  left: 0.65rem;
  width: 6rem;
  font-size: 0.25rem;
  //color: #f2270c;
  .contentSpan {
    margin-left: 0.39rem;
    display: flex;
    position: absolute;
    top: -0.06rem;
    left: 2.07rem;

    .acblockStyleStyle {
      color: #ffffff;
      border-radius: 0.05rem;
      display: flex;
      font-size: 0.25rem;
      justify-content: center;
      align-items: center;
      //background-color: #8b4004;
      background: url("https://img10.360buyimg.com/imgzone/jfs/t1/87629/28/45149/462/64fdb7aeFf4528773/3b72d8f1a8ff9dbe.png") no-repeat;
      background-size: 100%;
      width: 0.44rem;
      height: 0.44rem;
    }
    span {
      width: 0.4rem;
      height: 0.44rem;
      //color: #ffdf9d;
      display: flex;
      font-size: 0.25rem;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
