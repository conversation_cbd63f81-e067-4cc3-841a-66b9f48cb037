<template>
  <div class="rule-bk-upload">
    <div class="rule-bk" :style="furnish.upLoadPop ? {'backgroundImage': 'url(' + furnish.upLoadPop + ')' } : ''">
      <div class="titleDiv">
        <div>*请在光源充足的环境拍摄</div>
        <div>*确保证件四角都在框线内</div>
      </div>
      <div class="uploadDivAll">
        <div class="uploadImgDiv1" @click="uploadImg1Click()">
          <img v-if="previewUrl" :src="previewUrl" alt="" />
        </div>
        <div class="uploadImgDiv2" @click="uploadImg2Click()">
          <img v-if="previewUrl2" :src="previewUrl2" alt="" />
        </div>

        <div class="uploadImgDiv3" @click="uploadImg3Click()">
          <video v-if="videoUrl3" :src="videoUrl3" controls="" />
        </div>

        <!-- 隐藏的input -->
        <input
          ref='fileInput'
          type='file'
          accept='image/*'
          class='hidden-input'
          @change='handleFileChange'
        />
        <input
          ref='fileInput2'
          type='file'
          accept='image/*'
          class='hidden-input'
          @change='handleFileChange2'
        />
        <input
          ref='fileInput3'
          type='file'
          accept='video/*'
          class='hidden-input'
          @change='handleFileChange3'
        />
      </div>
      <div class="btnDivAll" v-if="fileType === 1">
        <div class="resetUploadBtn" @click="resetUploadClick()">重新上传</div>
        <div class="saveBtn" @click="saveImgClick()">保存</div>
      </div>
    </div>
    <div class="closeDiv" @click="close()"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { showLoadingToast, showToast, closeToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { furnish } from '../ts/furnishStyles';

const props = defineProps({
  fileType: {
    type: Number,
    default: 1
  },
  babyImg: {
    type: String,
    default: '',
  },
  parentsImg: {
    type: String,
    default: '',
  },
  video: {
    type: String,
    default: '',
  },
  isCanSubImg: {
    type: Boolean,
    default: true
  },
  isCanSubParentsImg: {
    type: Boolean,
    default: true
  },
  isCanSubVideo: {
    type: Boolean,
    default: true
  }
});
const previewUrl = ref(props.babyImg);
const previewUrl2 = ref(props.parentsImg);
const videoUrl3 = ref(props.video);

const fileInput = ref(null);
const fileInput2 = ref(null);
const fileInput3 = ref(null);
const emits = defineEmits(['close', 'saveImg']);

const close = () => {
  emits('close');
};
// 上传出生证明
const uploadImg1Click = () => {
  console.log('上传出生证明');
  if (props.fileType!== 1 || previewUrl.value) {
    return;
  }
  fileInput.value.click();
};
// 上传出生证明
const uploadImg2Click = () => {
  console.log('上传出生证明');
  if (props.fileType!== 1 || previewUrl2.value) {
    return;
  }
  fileInput2.value.click();
};
const uploadImg3Click = () => {
  console.log('上传出生证明视频');
  if (props.fileType!== 1 || videoUrl3.value) {
    return;
  }
  fileInput3.value.click();
};
// 处理文件选择
const handleFileChange = async (event: any) => {
  const file = event.target.files[0];
  if (!file) return;
  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    showToast('请选择图片');
    return;
  }
  if (file) {
    // console.log(file);
    let formData = new FormData();
    formData.append('file', file);
    try {
      showLoadingToast({
        message: '加载中...',
        forbidClick: true,
        duration: 0,
      });
      const res = await httpRequest.post('/common/uploadImg', formData);
      previewUrl.value = res.data;
      closeToast();
    } catch (e) {
      showToast(e.message);
    }
  }
  event.target.value = '';
};
const handleFileChange2 = async (event: any) => {
  const file = event.target.files[0];
  if (!file) return;

  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    showToast('请选择图片');
    return;
  }

  // // 读取新文件
  // const reader = new FileReader();
  //
  // reader.onload = (event) => {
  //   previewUrl2.value = event.target.result;
  //   // console.log(previewUrl2.value, 'previewUrl.value==');
  // };
  // reader.readAsDataURL(file);
  if (file) {
    // console.log(file);
    let formData = new FormData();
    formData.append('file', file);
    try {
      showLoadingToast({
        message: '加载中...',
        forbidClick: true,
        duration: 0,
      });
      const res = await httpRequest.post('/common/uploadImg', formData);
      previewUrl2.value = res.data;
      closeToast();
    } catch (e) {
      showToast(e.message);
    }
  }
  event.target.value = '';
};
const handleFileChange3 = async (event: any) => {
  const file = event.target.files[0];
  if (!file) return;

  // 验证文件类型
  if (!file.type.startsWith('video/')) {
    showToast('请选择视频');
    return;
  }

  // 读取新文件
  // const reader = new FileReader();
  //
  // reader.onload = (event) => {
  //   videoUrl3.value = event.target.result;
  //   // console.log(previewUrl2.value, 'previewUrl.value==');
  // };
  // reader.readAsDataURL(file);
  if (file) {
    // console.log(file);
    let formData = new FormData();
    formData.append('file', file);
    try {
      showLoadingToast({
        message: '加载中...',
        forbidClick: true,
        duration: 0,
      });
      const res = await httpRequest.post('/common/uploadVideoOss', formData);
      videoUrl3.value = res.data;
      closeToast();
    } catch (e) {
      showToast(e.message);
    }
  }
  event.target.value = '';
};
const resetUploadClick = () => {
  previewUrl.value = '';
  previewUrl2.value = '';
  videoUrl3.value = '';
};
const saveImgClick = () => {
  if (props.isCanSubImg) {
    if (!previewUrl.value) {
      showToast('请上传宝宝出生证明');
      return;
    }
  }
  if (props.isCanSubParentsImg) {
    if (!previewUrl2.value) {
      showToast('请上传父母手持出生证明照片');
      return;
    }
  }
  if (props.isCanSubVideo) {
    if (!videoUrl3.value) {
      showToast('请上传出生证明视频');
      return;
    }
  }
  const data = {
    babyImg1: previewUrl.value,
    parentsImg1: previewUrl2.value,
    videoUrl1: videoUrl3.value,
  };
  emits('saveImg', data);
};
</script>

<style scoped lang="scss">
.rule-bk-upload{
  .rule-bk {
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/303536/28/3093/47529/6819dbe1F28acc7c0/6cf20e4a62110841.png) no-repeat;
    background-size: 100% 100%;
    width: 5.86rem;
    height: 5.90rem;
    padding-top: 0.5rem;
    .titleDiv{
      color: #e10505;
      font-size: 0.2rem;
      text-align: center;
    }
    .uploadDivAll{
      max-height: 3rem;
      overflow-y: scroll;
      margin-top: 0.4rem;
      .hidden-input {
        display: none;
      }
      .uploadImgDiv1{
        //background-color: red;
        width: 4.64rem;
        height: 2.79rem;
        margin-left: calc(50% - 4.64rem / 2);
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/300873/15/3146/2258/6819dbe5F36289f98/add762cfc925c37b.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          max-width: 100%;
          height: 100%;
        }
      }
      .uploadImgDiv2{
        //background-color: red;
        width: 4.64rem;
        height: 2.79rem;
        margin-left: calc(50% - 4.64rem / 2);
        margin-top: 0.3rem;
        margin-bottom: 0.3rem;
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/283309/3/29124/3435/6819dbe2Fad8480a0/9e8d5fe16ef9fe35.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          max-width: 100%;
          height: 100%;
        }
      }
      .uploadImgDiv3{
        //background-color: red;
        width: 4.64rem;
        height: 2.79rem;
        margin-left: calc(50% - 4.64rem / 2);
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/303106/19/3112/2646/6819dbe0Fa5e65e58/7edb7d77c8222637.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        video {
          width: 100%;
          height: 100%;
        }
      }
    }
    .btnDivAll{
      display: flex;
      justify-content: center;
      margin-top: 0.4rem;
      //position: absolute;
      //bottom: 0.2rem;
      .resetUploadBtn{
        font-size: 0;
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/293166/2/3302/3318/681a1195Fe3e418e3/4dffcd94e5e2e981.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 2.18rem;
        height: 0.49rem;
        margin-right: 0.1rem;
      }
      .saveBtn{
        font-size: 0;
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/297560/29/3695/3699/6819dbe4Fdf1f8bdc/9d2c3e417256571f.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 2.18rem;
        height: 0.49rem;
        margin-left: 0.1rem;
      }
    }
  }
  .closeDiv{
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/286119/10/3415/940/6819dbe2Fe4cd8ca0/3f09ad9189125925.png) no-repeat;
    background-size: 100% 100%;
    width:0.61rem;
    height: 0.61rem;
    margin-left: calc( 50% - 0.61rem / 2);
    margin-top: 0.4rem;
  }
}
</style>
