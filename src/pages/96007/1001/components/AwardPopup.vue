<template>
  <div class="bk" v-if="prize.prizeType !== 0">
    <div class="content">
      <div class="prize-name">
        <div>恭喜你！获得</div>
        <div>{{ prize.prizeName }}</div>
      </div>
      <img :src="prize.prizeImg" alt="" class="prize-img" />
      <div class="btn-list">
        <!--京豆，红包，积分（我知道了按钮）-->
        <div>
          <div class="btn2" v-if="prize.prizeType === 2 || prize.prizeType === 4 || prize.prizeType === 6" @click="close"/>
        </div>
        <!--实物:填写地址-->
        <div v-if="prize.prizeType === 3">
          <div class="btn3" @click="saveAddress"/>
          <div class="tips">
            奖品将在活动结束后<br/>
            30个工作日之内为您发放</div>
        </div>
        <!--礼品卡：复制卡密-->
        <div v-if="prize.prizeType === 7">
          <div class="btn4 copy-btn" :copy-text="getCopyText()"/>
        </div>
      </div>
    </div>
  </div>
  <div class="close" @click="close"></div>
</template>

<script lang="ts" setup>
import { BaseInfo } from '@/types/BaseInfo';
import Clipboard from "clipboard";
import {inject, ref} from 'vue';
import { showToast } from 'vant';

const baseInfo = inject('baseInfo') as BaseInfo;

const props = defineProps(['prize']);

const emits = defineEmits(['close','saveAddress']);

const close = () => {
  emits('close');
};

const saveAddress = () => {
  emits('saveAddress', props.prize.userPrizeId)
}
const text = ref('');
const getCopyText = () => {
  if (props.prize.result.cardNumber && props.prize.result.cardPassword && props.prize.prizeType === 7) {
    text.value = `卡号：${props.prize.result.cardNumber}\n密码：${props.prize.result.cardPassword}`;
  } else if (props.prize.prizeType === 7 && props.prize.result.cardNumber && !props.prize.result.cardPassword) {
    text.value = props.prize.result.cardNumber;
  }
  return text.value;
};

// 复制卡号
const clipboard = new Clipboard('.copy-btn', {
  text(trigger) {
    return trigger.getAttribute('copy-text') ?? '';
  },
})
  .on('success', () => {
    showToast('复制成功');
  })
  .on('error', () => {
    showToast('复制失败');
  });

</script>

<style scoped lang="scss">
.bk {
  height: 8.92rem;
  width: 6.5rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/301702/7/13754/26751/6847f5b0Fd7c21fb3/48e0aea125be79ed.png);
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 1rem;
  .prize-img {
    height: 2rem;
    margin: 0.8rem auto 1.3rem;
  }
  .content {
    width: 5.6rem;
    height: 3.5rem;
    border-radius: 0.2rem;
    margin: 0.2rem auto 0;
    padding: 0.3rem 0;
    .prize-name {
      font-size: 0.4rem;
      font-weight: bold;
      margin: 0 0 0.3rem;
      text-align: center;
      color: #002d81;
    }
    .btn-list {
      display: flex;
      justify-content: center;
      /*我知道了*/
      .btn2 {
        width: 2.51rem;
        height: 0.66rem;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/294412/15/13494/5787/6847f81bF19e7eada/55460a425a913ea2.png);
        background-repeat: no-repeat;
        background-size: 100%;
        margin: 0 auto;
      }
      /*填写地址*/
      .btn3 {
        width: 2.51rem;
        height: 0.66rem;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/305534/22/9583/5604/6847d0d5Fd694f18b/9681ec0e5a47b898.png);
        background-repeat: no-repeat;
        background-size: 100%;
        margin: 0 auto;
      }
      /*复制卡密*/
      .btn4 {
        width: 2.51rem;
        height: 0.66rem;
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/296978/33/14130/16140/6847f91aF463beee5/b70ba0f7ba9494e3.png);
        background-repeat: no-repeat;
        background-size: 100%;
        margin: 0 auto;
      }
      .tips{
        font-size: 0.24rem;
        text-align: center;
        color: #002c80;
        margin-top: 0.2rem;
      }
    }
  }
}
.close {
  width: 0.6rem;
  /* background: #000; */
  height: 0.6rem;
  margin: 0.6rem auto 0;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/310192/5/8413/1274/6847c989F1f4b5203/48aff4a323b38052.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
</style>
