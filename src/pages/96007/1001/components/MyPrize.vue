<template>
  <div class="prize-bk">
    <div class="content">
      <div v-for="(item, index) in prizes" :key="index" class="prize">
        <div :style="(item.prizeType === 3 && !item.address) ? {color: '#1c2288'} : ''">{{dayjs(item.createTime).format('YYYY-MM-DD')}}</div>
        <div class="name" :style="(item.prizeType === 3 && !item.address) ? {color: '#1c2288'} : ''">{{item.prizeName}}</div>
        <div>
          <!--填写地址-->
          <img @click="changAddress(item)" class="statusBtn" v-if="item.prizeType === 3 && !item.address" src="//img10.360buyimg.com/imgzone/jfs/t1/303542/36/9178/5795/6847ceefF1562ff2b/cfe8ae50d64d5780.png" alt="">
          <!--已发放-->
          <div>
            <!--实物填写了地址-->
            <span class="blueText" v-if="item.prizeType === 3 && item.address">已填写</span>
            <!--礼品卡：复制卡号-->
            <img class="statusBtn copy-btn" v-if="item.prizeType === 7" :copy-text="getCopyText(item.prizeContent)" src="//img10.360buyimg.com/imgzone/jfs/t1/310027/22/8418/5984/6847e1c0F40f7a96d/34fd7731e4e8a5f2.png" alt="">
            <!--已发放-->
            <span class="blueText" v-if="item.prizeType !== 3 && item.prizeType !== 7">已发放</span>
          </div>
        </div>
      </div>
      <div v-if="!prizes.length" class="no-data">暂无记录</div>
    </div>
  </div>
  <div class="close" @click="close"/>

  <VanPopup teleport="body" v-model:show="showSaveAddress">
    <SaveAddress
      v-if="showSaveAddress"
      :addressId="addressId"
      :echoData="echoData"
      :userPrizeId="userPrizeId"
      @close="closeSaveAddress"
    />
  </VanPopup>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';
import { inject, reactive, ref } from 'vue';
import { showLoadingToast, closeToast, showToast } from 'vant';
import SaveAddress from './SaveAddress.vue';
import { httpRequest } from '@/utils/service';
import { FormType } from '../ts/type';
import Clipboard from "clipboard";
import { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;
const isPreview = inject('isPreview') as boolean;
const emits = defineEmits(['close']);
const close = () => {
  emits('close');
};

const prizeType = {
  0: '谢谢参与',
  1: '优惠券',
  2: '京豆',
  3: '实物',
  4: '积分',
  5: '专享价',
  6: '红包',
  7: '礼品卡',
  8: '京东e卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '自营令牌促销',
  12: '京元宝',
};

interface Prize {
  prizeType: number;
  prizeImg: string;
  prizeName: string;
  createTime: string;
  addressId: string;
  prizeContent: string;
  userPrizeId: string;
  isFuLuWaitingReceive: boolean;
  deliveryStatus: number;
  deliverName: string;
  deliverNo: string;
}

const prizes = reactive([] as Prize[]);
const currentPrizeId = ref('');

const getCopyText = (content:any) => {
  let text = '';
  const prizeContent = JSON.parse(content);
  if (prizeContent.cardNumber && prizeContent.cardPassword) {
    text = `卡号：${prizeContent.cardNumber}\n密码：${prizeContent.cardPassword}`;
  } else if (prizeContent.cardNumber && !prizeContent.cardPassword) {
    text = prizeContent.cardNumber;
  }
  return text;
};

// 复制卡号
const clipboard = new Clipboard('.copy-btn', {
  text(trigger) {
    return trigger.getAttribute('copy-text') ?? '';
  },
})
  .on('success', () => {
    showToast('复制成功');
  })
  .on('error', () => {
    showToast('复制失败');
  });


const getUserPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/96007/userPrizes');
    closeToast();
    prizes.splice(0);
    prizes.push(...res.data);
  } catch (error) {
    closeToast();
    showToast(error.message);
    console.error(error);
  }
};

if (!isPreview) {
  getUserPrizes();
}

const showSaveAddress = ref(false);
const addressId = ref('');
const activityPrizeId = ref('');
const echoData: FormType = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
});

// 填写地址
const changAddress = (item: any) => {
  if (baseInfo.status === 3 && !item.realName) {
    showToast('活动已结束~');
    return;
  }
  addressId.value = item.addressId;
  activityPrizeId.value = item.userPrizeId;
  Object.keys(echoData).forEach((key) => {
    echoData[key] = item[key];
  });
  showSaveAddress.value = true;
};

// 关闭收货地址
const closeSaveAddress = (type: boolean | undefined) => {
  showSaveAddress.value = false;
  if (type) {
    setTimeout(() => {
      getUserPrizes();
    }, 1000);
  }
};
</script>

<style scoped lang="scss">
.blueText{
  color: #002d81;
  font-style: italic;
}
.prize-bk {
  border-radius: 0.2rem 0.2rem 0 0;
  width: 6.5rem;
  height: 8.92rem;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/308912/9/8289/24224/6847cc56Ff1f15b48/1d72334af76812e0.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 1.8rem 0 0 0;
  .content {
    height: 6.6rem;
    width: 6rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #002d81;
    white-space: pre-wrap;
    cursor: pointer;
    margin: 0 auto;
    border-radius: 0.2rem;
    /*background-color: #000;*/

    .prize {
      margin-bottom: 0.1rem;
      margin-top: 0.1rem;
      /* padding-bottom: 0.24rem; */
      border-radius: 0.16rem;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      div {
        flex:1;
        text-align: center;
      }
      .name{
        flex: 2;
      }
      .statusBtn{
        height: 0.38rem;
        width: 1.38rem;
        margin: 0 auto;
      }
    }

    .no-data {
      text-align: center;
      margin-top: 2.4rem;
      font-size: 0.24rem;
      color: #8c8c8c;
    }
  }
}
.close {
  width: 0.6rem;
  /* background: #000; */
  height: 0.6rem;
  margin: 0.6rem auto 0;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/310192/5/8413/1274/6847c989F1f4b5203/48aff4a323b38052.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
</style>
