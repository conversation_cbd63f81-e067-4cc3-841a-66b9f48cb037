<template>
  <div class="rule-bk">
    <div class="openCard" v-click-track="'cwhy'" @click="openCard"></div>
  </div>
  <div class="close" @click="close"/>
</template>

<script lang="ts" setup>
import { BaseInfo } from '@/types/BaseInfo';
import {inject} from 'vue';

const baseInfo = inject('baseInfo') as BaseInfo;

const emits = defineEmits(['close']);

const openCard = () => {
  window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`;
};

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  border-radius: 0.2rem 0.2rem 0 0;
  width: 6.5rem;
  height: 8.92rem;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/295644/9/5658/114243/6847eb10F1a670442/08df937fb92032bf.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 5.25rem 0 0 0;

  .openCard{
    width: 2.6rem;
    height: 0.66rem;
    margin: 0 auto;
    /* background-color: aqua;*/
  }

}
.close {
  width: 0.6rem;
  /* background: #000; */
  height: 0.6rem;
  margin: 0.6rem auto 0;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/310192/5/8413/1274/6847c989F1f4b5203/48aff4a323b38052.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
</style>
