<template>
  <div class="order-bk">
    <div class="content">
      <div class="prizeTitle">
        <div>订单编号</div>
        <div>下单时间</div>
        <div>状态</div>
      </div>
      <div v-for="(item, index) in orderData" :key="index" class="prize">
        <div>{{item.orderId}}</div>
        <div class="time">{{dayjs(item.orderStartTime).format('YYYY-MM-DD') }}</div>
        <div>
          <!--未兑换-->
          <img class="statusBtn" v-if="item.status === 0" src="//img10.360buyimg.com/imgzone/jfs/t1/312061/15/8294/5573/6847e6b5Fbbf2d3ec/0f59f94f1f92b9be.png" alt="">
          <!--已兑换-->
          <div class="blueText" v-if="item.status === 1">已兑换</div>
        </div>
      </div>

      <div v-if="!orderData?.length" class="no-data">暂无记录</div>
    </div>
  </div>
  <div class="close" @click="close"/>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { closeToast, showLoadingToast } from 'vant';
import { httpRequest } from '@/utils/service';
import dayjs from 'dayjs';
import { isPreview } from '@/utils';

interface orderInfo {
  orderId: string;
  orderStartTime: string;
  status: string; // 0-未兑换 1-已兑换
}
const orderData = ref([] as orderInfo[]);
const props = defineProps(['showOrderType']);

const emits = defineEmits(['close']);
const close = () => {
  emits('close');
};
const getMyOrder = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/96007/myOrders');
    orderData.value = res.data;
    closeToast();
  } catch (error) {
    closeToast();
    console.error(error);
  }
};

!isPreview && getMyOrder();
</script>

<style scoped lang="scss">
.order-bk {
  border-radius: 0.2rem 0.2rem 0 0;
  width: 6.52rem;
  height: 8.74rem;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/311117/29/8422/29206/6847e9f7Fb3024a01/d10a805c44b0852d.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 2rem 0.4rem 0;
  font-size: 0.24rem;
  .content {
    height: 5.3rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #002d81;
    white-space: pre-wrap;
    cursor: pointer;
    margin: 0 auto;
    border-radius: 0.2rem;
    /*background-color: #000;*/
    .prizeTitle{
      margin-bottom: 0.1rem;
      margin-top: 0.1rem;
      font-size: 0.3rem;
      font-style: italic;
      color: #002d81;
      border-radius: 0.16rem;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      div {
        flex:1;
        text-align: center;
      }
    }
    .prize {
      margin-bottom: 0.1rem;
      margin-top: 0.1rem;
      /* padding-bottom: 0.24rem; */
      border-radius: 0.16rem;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      div {
        flex:1;
        text-align: center;
      }
      .time {
        flex:1;
      }
      .statusBtn{
        //height: 0.28rem;
        width: 1.2rem;
        margin: 0 auto;
      }
      .blueText{
        color: #002d81;
        font-style: italic;
      }
    }

    .no-data {
      text-align: center;
      margin-top: 2.4rem;
      font-size: 0.24rem;
      color: #8c8c8c;
    }
  }
}
.close {
  width: 0.6rem;
  /* background: #000; */
  height: 0.6rem;
  margin: 0.6rem auto 0;
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/310192/5/8413/1274/6847c989F1f4b5203/48aff4a323b38052.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
</style>
