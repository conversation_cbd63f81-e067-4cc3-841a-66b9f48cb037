import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { init, initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};

const _decData = {
  actBg: '//img10.360buyimg.com/imgzone/jfs/t1/302746/17/12788/209997/684654d6F61703ac1/0a4a0c14102a3634.png',
  actBgColor: '#0051c4',
  shopNameColor: '#fff',
  ruleBtn: '//img10.360buyimg.com/imgzone/jfs/t1/316354/40/7934/5228/68465081Ffa210258/d9427d53d5a6a10b.png',
  myPrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/312838/22/8083/5260/68465080Fd9bd41aa/9bb280136cd83703.png',
  myOrderBtn: '//img10.360buyimg.com/imgzone/jfs/t1/296701/34/14125/5039/68465080F9d0db488/e107ec9fb2d0bb1a.png',
  step1Bg: '//img10.360buyimg.com/imgzone/jfs/t1/305215/27/9119/91547/68465083Ffacbebd2/9da42f5e2ceb7e2f.png',
  skuItemBg: '//img10.360buyimg.com/imgzone/jfs/t1/301462/37/13268/10615/68465121F46705a3d/59817af780a287f4.png',
  step2Bg: '//img10.360buyimg.com/imgzone/jfs/t1/320050/32/7317/139562/68465080Fed8f10c4/b89fa116bc799f50.png',
  step3Bg: '//img10.360buyimg.com/imgzone/jfs/t1/301942/12/12867/115073/68465082Fc2497b3c/bfe0698c94603a1d.png',
  step4Bg: '//img10.360buyimg.com/imgzone/jfs/t1/305769/14/9167/259658/68465081F3ab672f0/a07ee757d375c3d1.png',
  disableShopName: 0,
  cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/321226/6/7514/59631/684656a3Fc1035aa4/09aca824a99ad0d1.png',
  h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/309858/22/7992/37847/684656a3F75b5ccff/f71a0e475cf27507.png',
  mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/298937/20/14046/73961/684656a3F4b88ab49/db578093bdfcf724.png'
};

initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || 'QQ星评价有礼';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  // app.provide('decoData', _decData);
  app.provide('isPreview', true);
  app.mount('#app');
});
