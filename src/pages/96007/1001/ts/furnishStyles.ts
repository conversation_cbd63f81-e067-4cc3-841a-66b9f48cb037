import { computed, reactive } from 'vue';

export const prizeInfo = reactive([
  {
    id: '',
    index: 1,
    prizeImg: '',
    prizeName: '谢谢参与',
  },
  {
    id: '',
    index: 2,
    prizeImg: '',
    prizeName: '谢谢参与',
  },
  {
    id: '',
    index: 3,
    prizeImg: '',
    prizeName: '谢谢参与',
  },
  {
    id: '',
    index: 4,
    prizeImg: '',
    prizeName: '谢谢参与',
  },
  {
    id: '',
    index: 5,
    prizeImg: '',
    prizeName: '谢谢参与',
  },
  {
    id: '',
    index: 6,
    prizeImg: '',
    prizeName: '谢谢参与',
  },
  {
    id: '',
    index: 7,
    prizeImg: '',
    prizeName: '谢谢参与',
  },
  {
    id: '',
    index: 8,
    prizeImg: '',
    prizeName: '谢谢参与',
  },
]);

export const furnish = reactive({
  actBg: '',
  actBgColor: '',
  shopNameColor: '',
  ruleBtn: '',
  myPrizeBtn: '',
  myOrderBtn: '',
  step1Bg: '',
  skuItemBg: '',
  step2Bg: '',
  step3Bg: '',
  step4Bg: '',
  disableShopName: 0,
  cmdImg: '',
  h5Img: '',
  mpImg: '',
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
}));

const shopNameColor = computed(() => ({
  color: furnish.shopNameColor ?? '',
}));

const ruleBtn = computed(() => ({
  backgroundImage: furnish.ruleBtn ? `url(${furnish.ruleBtn})` : '',
}));

const myPrizeBtn = computed(() => ({
  backgroundImage: furnish.myPrizeBtn ? `url(${furnish.myPrizeBtn})` : '',
}));

const myOrderBtn = computed(() => ({
  backgroundImage: furnish.myOrderBtn ? `url(${furnish.myOrderBtn})` : '',
}));

const step1Bg = computed(() => ({
  backgroundImage: furnish.step1Bg ? `url(${furnish.step1Bg})` : '',
}));

const step2Bg = computed(() => ({
  backgroundImage: furnish.step2Bg ? `url(${furnish.step2Bg})` : '',
}));

const step3Bg = computed(() => ({
  backgroundImage: furnish.step3Bg ? `url(${furnish.step3Bg})` : '',
}));

const step4Bg = computed(() => ({
  backgroundImage: furnish.step4Bg ? `url(${furnish.step4Bg})` : '',
}));

export default {
  pageBg,
  shopNameColor,
  ruleBtn,
  myPrizeBtn,
  myOrderBtn,
  step1Bg,
  step2Bg,
  step3Bg,
  step4Bg,
};
