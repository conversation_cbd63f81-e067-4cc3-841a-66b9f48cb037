<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv">
      <img :src="furnish.actBg" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>
        <div>
          <div class="header-btn" :style="furnishStyles.ruleBtn.value" v-click-track="'hdgz'" @click="()=>showRulePopup()"/>
          <div class="header-btn" :style="furnishStyles.myPrizeBtn.value" v-click-track="'wdjp'" @click="showMyPrize = true"/>
          <div class="header-btn" :style="furnishStyles.myOrderBtn.value" v-click-track="'wddd'" @click="showMyOrder = true"/>
        </div>
      </div>
    </div>
    <div class="sku" :style="furnishStyles.step1Bg.value">
      <div class="sku-list">
        <div class="sku-item" v-for="(item, index) in evaluateSkuList" :key="index">
          <div class="sku-name">{{ item.skuName }}</div>
          <img :src="item.skuMainPicture" alt="" />
          <div class="sku-btn" @click="gotoSkuPage(item.skuId)"/>
        </div>
        <div class="more-btn" v-if="evaluateSkuList.length > 18" @click="loadMore">点我加载更多</div>
      </div>
    </div>
    <div class="prizeBox" :style="furnishStyles.step2Bg.value">
      <div class="picture">
        <img :src="activePrize.prizeImg ? activePrize.prizeImg : ''" alt="">
      </div>
      <div class="text">
        <div class="name">可领{{activePrize.prizeName}}</div>
        <div class="remain">（限量{{activePrize.num}}份，先到先得）</div>
        <div class="btn" :class="activePrize.status === 0 ? '' : 'grayBtn'" v-click-track="'ljlq'" @click="receivePrize"></div>
      </div>
    </div>
    <div class="step3" :style="furnishStyles.step3Bg.value"/>
    <div class="step4" :style="furnishStyles.step4Bg.value"/>
  </div>
  <!-- 规则弹窗 -->
  <VanPopup teleport="body" v-model:show="showRule">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
  <!-- 我的奖品弹窗 -->
  <VanPopup teleport="body" v-model:show="showMyPrize">
    <MyPrize v-if="showMyPrize" @close="showMyPrize = false" @showCardNum="showCardNum"></MyPrize>
  </VanPopup>
  <!-- 中奖弹窗 -->
  <VanPopup teleport="body" v-model:show="showAward">
    <AwardPopup :prize="award" @close="showAward = false" @saveAddress="toSaveAddress"></AwardPopup>
  </VanPopup>
  <!-- 我的订单弹窗 -->
  <VanPopup teleport="body" v-model:show="showMyOrder">
    <MyOrder v-if="showMyOrder" @close="showMyOrder = false"></MyOrder>
  </VanPopup>
  <!-- 保存地址弹窗 -->
  <VanPopup teleport="body" v-model:show="showSaveAddress">
    <SaveAddress v-if="showSaveAddress" :addressId="addressId" :userPrizeId="userPrizeId" @close="showSaveAddress = false"></SaveAddress>
  </VanPopup>
  <!-- 非会员开卡提示弹窗-->
  <VanPopup teleport="body" v-model:show="openCardPopup" :closeOnClickOverlay="false">
    <OpenCard @close="openCardPopup = false"></OpenCard>
  </VanPopup>
</template>

<script setup lang="ts">
import { ref, reactive, inject } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import RulePopup from '../components/RulePopup.vue';
import MyPrize from '../components/MyPrize.vue';
import AwardPopup from '../components/AwardPopup.vue';
import SaveAddress from '../components/SaveAddress.vue';
import MyOrder from "../components/MyOrder.vue";
import OpenCard from '../components/OpenCard.vue';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { Sku, CardType } from '../ts/type';
import { BaseInfo } from '@/types/BaseInfo';
import { gotoSkuPage } from '@/utils/platforms/jump';
import constant from '@/utils/constant';
import { callShare } from '@/utils/platforms/share';

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;

const shopName = ref(baseInfo.shopName);

const showRule = ref(false);
const showMyPrize = ref(false);
const showMyOrder = ref(false);
const openCardPopup = ref(false);

const ruleTest = ref('');
// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};

const evaluateSkuList = ref<Sku[]>([]);
const pageNum = ref(1);
const pagesAll = ref(0);

interface ActivePrize {
  prizeId: number;
  prizeImg: string;
  prizeName: string;
  leftNum: number; // 奖品剩余库存
  status:number; //	按钮状态 0-可以领取 1-罐数不足 2-奖品库存不足
}
const activePrize = ref<ActivePrize>({});

const showAward = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  prizeImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});

// 保存实物地址相关
const showSaveAddress = ref(false);
const userPrizeId = ref('');
const addressId = ref('');
const toSaveAddress = (id: string) => {
  userPrizeId.value = id;
  showAward.value = false;
  showSaveAddress.value = true;
};

// 卡密
const cardDetail = reactive({
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  id: 1,
  prizeName: '',
  prizeImg: '',
});

const showCardNum = (result: CardType) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = result[item];
  });
};

// 获取奖品信息
const getPrizes = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/96007/getPrizes');
    activePrize.value = data;
    console.log(data, '奖品信息', activePrize.value);
    closeToast();
  } catch (error) {
    closeToast();
    console.error(error);
  }
};

// 获取sku列表
const getExposureSku = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/96007/getExposureSkuPage', {
      type: 1,
      pageNum: pageNum.value,
      pageSize: 10,
    });
    closeToast();
    if (res.code === 200) {
      evaluateSkuList.value.push(...res.data.records);
      pagesAll.value = res.data.pages;
    }
  } catch (error) {
    closeToast();
    console.error(error);
  }
};

// 加载更多sku
const loadMore = async () => {
  pageNum.value++;
  await getExposureSku();
};

// 领取奖品
const receivePrize = async () => {
  if (baseInfo.thresholdResponseList.length && baseInfo.thresholdResponseList[0].thresholdCode === 4) {
    openCardPopup.value = true;
    console.log('您不是会员');
    return;
  }
  // 活动是否开始
  const now = new Date().getTime();
  if (now < baseInfo.startTime) {
    console.log('活动未开始')
    showToast('活动未开始~');
    return;
  }
  // 活动是否结束
  if (now > baseInfo.endTime) {
    showToast('活动已结束~');
    return;
  }
  // 领奖机会不足
  if (activePrize.value.status === 1) {
    showToast('您暂无领奖机会~')
    return;
  }
  // 奖品库存不足
  if (activePrize.value.status === 2) {
    showToast('奖品库存不足~');
    return;
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/96007/receivePrize', { prizeId: activePrize.value.prizeId });
    award.value = data.distribute;
    console.log(data, '兑换结果');
    closeToast();
    showAward.value = true;
    await getPrizes();
  } catch (error) {
    closeToast();
    showToast(error.message);
    console.error(error);
  }
}

const init = async () => {
  console.log(decoData);
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  if (baseInfo.thresholdResponseList.length && baseInfo.thresholdResponseList[0].thresholdCode === 4) {
    openCardPopup.value = true;
    console.log('您不是会员');
  }
  try {
    await Promise.all([getPrizes(), getExposureSku()]);
  } catch (error) {
    closeToast();
  }
};
init();

const shareAct = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};
</script>

<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom: 0.3rem;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0;
    display: flex;
    justify-content: space-between;
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    padding: 0 0.2rem;
    margin-bottom: 0.1rem;
    background-repeat: no-repeat;
    background-size: 100%;
    width: 1.18rem;
    height: 0.44rem;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}

.sku {
  width: 6.82rem;
  height: 9.49rem;
  margin: 0 auto;
  padding: 1rem 0.3rem 0;
  background-size: 100% 100%;

  .more-btn {
    width: 1.8rem;
    height: 0.5rem;
    font-size: 0.2rem;
    color: #fff;
    background: -webkit-gradient(linear, left top, right top, from(#f2270c), to(#ff6420));
    background: linear-gradient(90deg, #f2270c 0%, #ff6420 100%);
    border-radius: 0.25rem;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0.24rem auto 0.3rem;
  }
  .title-img {
    width: 2.82rem;
    height: 0.4rem;
    margin: 0 auto;
    background-repeat: no-repeat;
    background-size: 100%;
  }
  .sku-list {
    justify-content: space-between;
    flex-wrap: wrap;
    display: flex;
    margin: 0.2rem auto 0.1rem auto;
    padding:0;
    height: 8rem;
    overflow: hidden;
    /* background-color: aqua; */
    overflow-y: scroll;
    .sku-item {
      margin: 0.2rem 0 0 0;
      border-radius: 0.2rem;
      width: 3.08rem;
      height: 3.73rem;
      /*background: rgb(255, 255, 255);*/
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/301462/37/13268/10615/68465121F46705a3d/59817af780a287f4.png);
      background-size: 100%;
      background-repeat: no-repeat;
      overflow: hidden;
      .sku-name{
        color: #fff;
        width: 2.2rem;
        height: 0.45rem;
        line-height: 0.45rem;
        /* background-color: antiquewhite; */
        padding: 0 0.1rem 0 0.15rem;
        font-size: 0.22rem;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .sku-btn{
        width: 2rem;
        height: 0.48rem;
        margin: 0.32rem auto 0;
      }
      img{
        height: 2rem;
        margin: 0.45rem auto 0;
      }
    }
  }
}
.grayBtn{
  filter: grayscale(100%);
}
.prizeBox{
  width: 6.82rem;
  height: 3.37rem;
  margin: 0 auto;
  padding: 1.2rem 0.3rem 0.4rem;
  background-size: 100%;
  display: flex;
  .picture{
    flex: 1.2;
    img{
      height: 2rem;
      margin: 0 auto;
    }
  }
  .text{
    text-align: center;
    flex: 2;
    padding: 0.6rem 0;
    color: #061e72;
    .name {
      font-size: 0.24rem;
    }
    .remain{
      font-size: 0.14rem;
    }
    .btn{
      width: 1.74rem;
      height: 0.44rem;
      background: url(//img10.360buyimg.com/imgzone/jfs/t1/305357/29/9207/8898/68465081Fdeb3ea68/8bd16fa120374b09.png) no-repeat;
      background-size: 100%;
      margin: 0.1rem auto 0;
    }
  }
}

.step3{
  width: 6.82rem;
  height: 4.57rem;
  margin: 0 auto;
  background-size: 100%;
}

.step4{
  width: 6.82rem;
  height: 4.45rem;
  margin: 0 auto;
  background-size: 100%;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
