<template>
  <VanPopup v-model:show="isShow" @open="getTaskSkuList">
    <div class="popup-bg">
      <div class="sku-list">
        <div class="sku-card" v-for="(item, index) in skuList" :key="index">
          <img class="image" :src="item.imagePath" alt="" />
          <div class="name">{{ item.name }}</div>
          <img class="btn" src="//img10.360buyimg.com/imgzone/jfs/t1/306425/16/6143/3932/68380224Fbd0b5401/c00914f0a9ff7cf4.png" alt="" @click="goSku(item.skuId)" />
        </div>
      </div>
      <img class="close" src="//img10.360buyimg.com/imgzone/jfs/t1/288245/31/9579/1772/6836db46Fb2c9f682/860212cf7162771c.png" @click="close" />
    </div>
  </VanPopup>
</template>
<script setup lang="ts">
import { computed, ref } from 'vue';
import { SkuListRecord } from '../script/type';
import { getSkuList } from '../script/ajax';
import { gotoSkuPage } from '@/utils/platforms/jump';
import { showLoadingToast, showToast, closeToast } from 'vant';

const props = defineProps({
  showPlaceAnOrder: {
    type: Boolean,
    default: false,
  },
});
const skuList = ref<SkuListRecord[]>([]);
const emit = defineEmits(['close']);
// 获取sku列表
const getTaskSkuList = async () => {
  skuList.value = await getSkuList(3);
};
const goSku = (skuId: string) => {
  gotoSkuPage(skuId);
  setTimeout(() => {
    showToast('观看成功,获得50积分');
  }, 1000);
};
const close = () => {
  emit('close');
};
const isShow = computed(() => props.showPlaceAnOrder);
</script>
<style lang="scss" scoped>
.popup-bg {
  width: 6.5rem;
  height: 8.5rem;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/292078/6/10870/50496/68380223F633f4c1f/82d2a255b0f74811.png') no-repeat;
  background-size: 100%;
  padding-top: 1.4rem;
  .sku-list {
    width: 5.6rem;
    height: 6.5rem;
    margin: 0 auto;
    overflow-y: scroll;
    font-size: 0.24rem;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-evenly;
    .sku-card {
      width: 2.5rem;
      height: 3.2rem;
      background: url('//img10.360buyimg.com/imgzone/jfs/t1/290565/37/9291/35713/6838023cF38dc2c95/22d47d49b90858c6.png') no-repeat;
      background-size: 100%;
      padding: 0.2rem 0.1rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 0.1rem;
      .image {
        width: auto;
        height: 1.6rem;
      }
      .name {
        width: 90%;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        margin-top: 0.1rem;
        font-size: 0.2rem;
      }
      .btn {
        width: 1.5rem;
      }
    }
  }
  .close {
    width: 0.6rem;
    height: 0.6rem;
    position: absolute;
    top: 0.1rem;
    right: 0.2rem;
  }
}
</style>
