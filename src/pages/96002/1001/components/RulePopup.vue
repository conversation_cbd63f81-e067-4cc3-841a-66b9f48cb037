<template>
  <div class="box">
    <div class='dialog'>
      <div class="dialog_rule" v-html="rule"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);
</script>

<style scoped lang="scss">
.box {

  .dialog {
    width: 6.5rem;
    margin: 0 auto;
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/299968/23/313/11351/680f14e5Fd6501a8e/f37e4889e4c05a0e.png) no-repeat;
    height: 8rem;
    background-size: cover;
    box-sizing: border-box;
    padding: 1.5rem 0.5rem 0.5rem;

    .dialog_rule {
      height: 6rem;
      overflow-y: auto;
      font-size: 0.3rem;
      font-weight: normal;
      letter-spacing: 0.01rem;
      color: #fff;
      margin-top: 0.1rem;
      text-align: left;
      white-space: pre-wrap;
    }
  }

}
</style>
