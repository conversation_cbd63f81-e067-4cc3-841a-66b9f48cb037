<template>
  <div class="join-bg">
    <div class="toFinishInfo" v-click-track="'ljqw'" @click="toFinishInfo"></div>
  </div>
</template>

<script lang="ts" setup>
import {inject} from 'vue';
import {BaseInfo} from '@/types/BaseInfo';
import furnishStyles, { furnish } from '../ts/furnishStyles';
const baseInfo = inject('baseInfo') as BaseInfo;

const emits = defineEmits(['close']);

const toFinishInfo = () => {
  console.log(furnish.finishInfoLink)
  window.location.href = furnish.finishInfoLink;
};

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.join-bg {
  width: 6.5rem;
  height: 8rem;
  margin: 0 auto;
  text-align: center;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/282848/38/25423/96802/680ef63fF524f8269/92da4051d47d3f6b.png);
  background-repeat: no-repeat;
  background-size: 100%;
  padding: 6.8rem 0.5rem 0;

  .toFinishInfo{
    width: 2.8rem;
    height: 0.7rem;
    border-radius: 0.1rem;
    margin: 0.2rem auto;
    //background: #fff;
  }
}
</style>
