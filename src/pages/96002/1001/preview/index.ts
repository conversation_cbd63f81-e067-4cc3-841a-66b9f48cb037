import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
const a = {
  "actBg": "",
  "pageBg": "//img10.360buyimg.com/imgzone/jfs/t1/288712/2/712/103662/680df0abFa7fc0ab9/a5d21fe54ab04746.png",
  "actBgColor": "#afcae2",
  "getBtnBg": "//img10.360buyimg.com/imgzone/jfs/t1/272951/38/28475/5539/680df072F29c72d14/c6fa0792034aadb7.png",
  "getPrizePageBg":"//img10.360buyimg.com/imgzone/jfs/t1/298990/22/644/21392/680df064F93773720/047c99372f8616f1.png",
  "silverCardPrizesImg": "//img10.360buyimg.com/imgzone/jfs/t1/291957/39/648/129942/680df082Facae78b0/033d26bf9b5d183e.png",
  "goldCardPrizesImg": "//img10.360buyimg.com/imgzone/jfs/t1/228354/35/36051/142357/680df082Fc7b536fa/ad81470dc9101c9e.png",
  "platinumCardPrizesImg":"//img10.360buyimg.com/imgzone/jfs/t1/287584/38/575/122008/680df083Fca9ead34/d0b16a9ccfc28188.png",
  "diamondCardPrizesImg":"//img10.360buyimg.com/imgzone/jfs/t1/293439/15/688/120319/680df083F7aaa907f/988eb9cc11dccfe5.png",
  "mpImg": "//img10.360buyimg.com/imgzone/jfs/t1/272855/13/28727/49954/680df202F6a0a2f63/e3a44fda78d615a5.png",
  "cmdImg": "//img10.360buyimg.com/imgzone/jfs/t1/299763/20/667/55442/680df202F69a25343/1c50e853d512c811.png",
  "h5Img": "//img10.360buyimg.com/imgzone/jfs/t1/279398/24/27767/13904/680df202F2abedbad/129b65b03a7a6acd.png"
};
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || 'fresh生日礼';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  // app.provide('decoData', decoData);
  app.provide('decoData', a);
  app.provide('isPreview', true);
  app.mount('#app');
});
