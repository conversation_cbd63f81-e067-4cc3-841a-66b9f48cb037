<template>
  <div class="bg" v-if="activeKey === '1'" :style="furnishStyles.pageBg.value">
    <div class="getStartBtn" :style="furnishStyles.getBtnBg.value" @click="toast"></div>
  </div>
  <div v-else class="getPrizeBg" :style="furnishStyles.getPrizePageBg.value">
    <div class="centerArea">
      <div class="left" @click="left"></div>
      <div class="prize prizeList swiper-container" ref="swiperRef">
        <div class="swiper-wrapper">
          <div class="swiper-slide" v-for="(item, index) in levelArr" :key="index">
            <div class="prizeImg">
              <img :src="item.mainPrizeImg" alt="" />
            </div>
            <div class="getPrizeBtn" :style="furnishStyles.getBtnBg.value" @click="toast"></div>
          </div>
        </div>
      </div>
      <div class="right" @click="right"></div>
    </div>
  </div>
  <!-- 规则 -->
  <VanPopup teleport="body" v-model:show="showRule" >
    <RulePopup :rule="ruleTest" @close="showRule = false"/>
  </VanPopup>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, inject } from 'vue';
import { showToast } from 'vant';
import furnishStyles, { furnish, Prize } from '../ts/furnishStyles';
import html2canvas from 'html2canvas';
import RulePopup from '../components/RulePopup.vue';
import Swiper from "swiper";
const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;
const activeKey = ref('1');
const isLoadingFinish = ref(false);
const showRule = ref(false);

const levelArr = ref<any[]>([
  {
    name: '银卡会员',
    value: 2,
    gradeValue: 2,
    prizeId: '',
    skuId: '',
    prizeType: 11,
    prizeImg: '',
    mainPrizeImg: furnish.silverCardPrizesImg,
  },
  {
    name: '金卡会员',
    value: 3,
    gradeValue: 3,
    prizeId: '',
    skuId: '',
    prizeType: 11,
    prizeImg: '',
    mainPrizeImg: furnish.goldCardPrizesImg,
  },
  {
    name: '铂金卡会员',
    value: 4,
    gradeValue: 4,
    prizeId: '',
    skuId: '',
    prizeType: 11,
    prizeImg: '',
    mainPrizeImg: furnish.platinumCardPrizesImg,
  },
  {
    name: '白钻卡会员',
    value: 5,
    gradeValue: 5,
    prizeId: '',
    skuId: '',
    prizeType: 11,
    prizeImg: '',
    mainPrizeImg: furnish.diamondCardPrizesImg,
  },
]);

const toast = () => {
  showToast('活动预览，仅供查看');
};
const dataURLToBlob = (dataurl: any) => {
  const arr = dataurl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  const n = bstr.length;
  const u8arr = new Uint8Array(n);
  for (let i = 0; i < bstr.length; i += 1) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  return new Blob([u8arr], { type: mime });
};

const ruleTest = ref('');

// 页面截图
const isCreateImg = ref(false);
const createImg = async () => {
  isCreateImg.value = true;
  nextTick(async () => {
    const canvas = await html2canvas(document.body, {
      useCORS: true,
      backgroundColor: null,
      scale: 1,
    });
    // 创建一个新的canvas来裁剪图片
    const cropCanvas = document.createElement('canvas');
    const ctx = cropCanvas.getContext('2d');
    cropCanvas.width = 375;
    cropCanvas.height = 670;

    // drawImage的参数为 source，sourceX, sourceY, sourceWidth, sourceHeight, destX, destY, destWidth, destHeight
    // 因为你没有指定从哪个位置开始裁剪，我默认为从(0, 0)位置开始
    ctx?.drawImage(canvas, 0, 0, canvas.width, (canvas.width / 375) * 670, 0, 0, 375, 670);

    // 获取裁剪后的图片
    const croppedBase64 = cropCanvas.toDataURL('image/png');
    isCreateImg.value = false;

    const blob = dataURLToBlob(croppedBase64);

    window.top?.postMessage(
      {
        from: 'C',
        type: 'screen',
        event: 'sendScreen',
        data: blob,
      },
      '*',
    );
  });
};
const prizeInfos = ref<Prize[]>([]); // 奖品信息
const taskRequestInfo = ref(
    {
      infoCheck: false, // 信息留存校验是否通过
      level: '', //等级
      memberCheck: false, // 会员信息校验是否通过
    },
);

const swiperRef = ref();
let mySwiper: Swiper | null = null;
let swiperInitialized = ref(false);

const initSwiper = () => {
  try {
    // 使用类选择器查找 Swiper 容器
    const swiperContainer = document.querySelector('.swiper-container');
    if (swiperContainer) {
      // 如果已经有 Swiper 实例，先销毁它
      if (mySwiper) {
        mySwiper.destroy(true, true); // 完全销毁，包括DOM元素和事件监听器
        mySwiper = null; // 确保引用被清除
      }
      // 创建新的 Swiper 实例
      setTimeout(() => {
        mySwiper = new Swiper('.swiper-container', {
          allowTouchMove: true,
          direction: 'horizontal',
          slidesPerView: 1,
          spaceBetween: 10,
          centeredSlides: true,
          loop: false,
          loopAdditionalSlides: 1, // 添加额外的slide以确保循环正常工作
        });
        console.log('Swiper initialized:', mySwiper);
        swiperInitialized.value = true;
      }, 200); // 增加延迟时间，确保DOM完全渲染
    }
  } catch (error) {
    console.error('Failed to initialize Swiper:', error);
    mySwiper = null;
  }
};

const left = () => {
  if (mySwiper && swiperInitialized.value) {
    mySwiper.slidePrev();
  } else if (!mySwiper) {
    initSwiper();
    setTimeout(() => {
      if (mySwiper) mySwiper.slidePrev();
    }, 300);
  }
};

const right = () => {
  if (mySwiper && swiperInitialized.value) {
    mySwiper.slideNext();
  } else if (!mySwiper) {
    initSwiper();
    setTimeout(() => {
      if (mySwiper) mySwiper.slideNext();
    }, 300);
  }
};

// 装修实时数据修改
const receiveMessage = (res: any) => {
  if (!res.data) return;
  if (res.data.from === 'C') return;
  const { data, type } = res.data;
  if (type === 'deco') {
    Object.keys(data).forEach((item) => {
      furnish[item] = data[item];
    });
    // 更新levelArr中的mainPrizeImg，确保与furnish中的值保持同步
    levelArr.value.forEach((item) => {
      if (item.gradeValue === 2) {
        item.mainPrizeImg = furnish.silverCardPrizesImg;
      } else if (item.gradeValue === 3) {
        item.mainPrizeImg = furnish.goldCardPrizesImg;
      } else if (item.gradeValue === 4) {
        item.mainPrizeImg = furnish.platinumCardPrizesImg;
      } else if (item.gradeValue === 5) {
        item.mainPrizeImg = furnish.diamondCardPrizesImg;
      }
    });
    isLoadingFinish.value = true;
    // 重新初始化swiper以显示更新后的图片
    // 确保在DOM更新后初始化swiper
    if (mySwiper) {
      mySwiper.destroy(true, true);
      mySwiper = null;
    }
    nextTick(() => {
      console.log('Reinitializing swiper after deco update');
      initSwiper();
    });
  } else if (type === 'activity') {
    if (data.taskRequestList.length) {
      taskRequestInfo.value = data.taskRequestList;
    }
    ruleTest.value = data.rules;
  } else if (type === 'screen') {
    createImg();
  } else if (type === 'activeKey') {
    activeKey.value = data;
    initSwiper();
  }
};

// 设置活动后的预览
onMounted(() => {
  window.addEventListener('message', receiveMessage, false);
  window.top?.postMessage(
    {
      from: 'C',
      type: 'mounted',
      event: 'sendMounted',
      data: true,
    },
    '*',
  );
  if (activityData) {
    if (activityData.taskRequestList.length) {
      taskRequestInfo.value = activityData.taskRequestList;
    }
    ruleTest.value = activityData.rules;
  }
  if (decoData) {
    // console.log(decoData);
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    levelArr.value.forEach((item) => {
      if (item.gradeValue === 2) {
        item.mainPrizeImg = furnish.silverCardPrizesImg;
      } else if (item.gradeValue === 3) {
        item.mainPrizeImg = furnish.goldCardPrizesImg;
      } else if (item.gradeValue === 4) {
        item.mainPrizeImg = furnish.platinumCardPrizesImg;
      } else if (item.gradeValue === 5) {
        item.mainPrizeImg = furnish.diamondCardPrizesImg;
      }
    });
    isLoadingFinish.value = true;
    if (mySwiper) {
      mySwiper.destroy(true, true);
      mySwiper = null;
    }
    nextTick(() => {
      console.log('Reinitializing swiper after deco update');
      initSwiper();
    });
  }
});
initSwiper();
</script>

<style>
::-webkit-scrollbar {
  width: 0 !important;
  display: none;
  height: 0;
}
</style>
<style scoped lang="scss">
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  position: relative;
  padding: 13rem 0 2rem 0;
  overflow: scroll;
  .getStartBtn {
    background-size: 100%;
    background-repeat: no-repeat;
    width: 3.7rem;
    height: 0.94rem;
    margin: 0 auto;
  }
}
.getPrizeBg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  position: relative;
  padding: 4rem 0 0 0;
  .centerArea {
    display: flex;
    height: 8rem;
    .left{
      width: 0.46rem;
      height: 0.46rem;
      background-size: 100%;
      background-repeat: no-repeat;
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/271297/22/27713/1496/680e0188F285d46d6/39b8be173fe5d8e0.png");
      display: flex;
      align-items: center;
      position: relative;
      top: 3.46rem;
      transform:translateY(50%);
      margin: 0 0.05rem 0 0.1rem;
      z-index: 10;
      cursor: pointer;
    }
    .swiper-slide {
      flex-shrink: 0;
      position: relative;
      transition-property: transform;
      width: 6.9rem;
      //display: flex;
      flex-wrap: wrap;
      //justify-content: flex-start;
      //gap: 0.2rem;
      align-content: flex-start;
    }

    .right {
      width: 0.46rem;
      height: 0.46rem;
      background-size: 100%;
      background-repeat: no-repeat;
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/294153/14/666/1430/680e0188F150d6683/f4172d7ac2c93c42.png");
      display: flex;
      align-items: center;
      position: relative;
      top: 3.46rem;
      transform:translateY(50%);
      margin: 0 0.1rem 0 0.05rem;
      z-index: 10;
      cursor: pointer;
    }
    .prize {
      width: 5.4rem;
      height: 11rem;
      margin: 0 auto;
      overflow: hidden;

      .swiper-container {
        width: 100%;
        height: 100%;
        overflow: hidden;
      }

      .swiper-wrapper {
        display: flex;
        width: 100%;
        height: 100%;
        transition-property: transform;
      }

      .swiper-slide {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        //justify-content: center;
        flex-shrink: 0;
      }

      .prizeImg{
        width: 5.4rem;
        height: 8rem;
        img{
          width: 100%;
        }
      }
      .getPrizeBtn {
        width: 3.7rem;
        height: 0.94rem;
        background-size: 100%;
        background-repeat: no-repeat;
        margin: 0.6rem auto 0;
      }
    }
  }
  .getPrizeBtn {
    width: 3.7rem;
    height: 0.94rem;
    background-size: 100%;
    background-repeat: no-repeat;
    margin: 0.6rem auto 0;
  }
}
.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}
</style>
