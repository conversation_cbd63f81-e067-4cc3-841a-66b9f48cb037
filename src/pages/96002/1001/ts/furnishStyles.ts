import { computed, reactive } from 'vue';

export interface Prize {
  prizeId: string;
  prizeImg: string;
  prizeMainImg: string;
  prizeLevel: string;
  taskId: string;
};
export const furnish = reactive({
  // 活动主页图
  actBg: '',
  // 页面背景图
  pageBg: '',
  getBtnBg: '',
  // 页面背景色
  actBgColor: '',
  // 领奖页面背景图
  getPrizePageBg: '',
  // 银卡奖品图
  silverCardPrizesImg: '',
  // 金卡奖品图
  goldCardPrizesImg: '',
  // 铂金卡奖品图
  platinumCardPrizesImg: '',
  // 白钻卡奖品图
  diamondCardPrizesImg: '',
  // 完善信息链接
  finishInfoLink: '',
  oneTitle: '',
  twoTitle: '',
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.pageBg ? `url("${furnish.pageBg}")` : '',
}));

const getBtnBg = computed(() => ({
  backgroundImage: furnish.getBtnBg ? `url("${furnish.getBtnBg}")` : '',
}));

const getPrizePageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
  backgroundImage: furnish.getPrizePageBg ? `url("${furnish.getPrizePageBg}")` : '',
}));

const silverCardPrizesImg = computed(() => ({
  backgroundImage: furnish.silverCardPrizesImg ? `url("${furnish.silverCardPrizesImg}")` : '',
}));

export default {
  pageBg,
  getBtnBg,
  getPrizePageBg,
  silverCardPrizesImg,
};
