<template>
  <div class="demo-group">
    <van-cell-group title="环境" inset>
      <my-cell title="jssdk版本" :value="jssdkVersion" system="" />
      <my-cell is-link title="是否京东APP" value="jmfe.isApp" @click="checkIsApp" system="012" />
      <my-cell is-link title="是否Android" value="jmfe.isAndroid" @click="checkIsAndroid" system="012" />
      <my-cell is-link title="是否iOS系统" value="jmfe.isIOS" @click="checkIsIOS" system="012" />
      <my-cell is-link title="是否Webview" value="jmfe.isWebview" @click="checkIsWebview" system="012" />
      <my-cell is-link title="是否平板设备" value="jmfe.isTablet" @click="checkTablet" system="01" />
      <my-cell is-link title="是否移动设备" value="jmfe.isMobile" @click="checkMobile" system="012" />
    </van-cell-group>
    <van-cell-group title="设备" inset>
      <my-cell is-link title="关闭Webview" value="jmfe.closeWebview" @click="closeWebview" system="012" />
      <my-cell is-link title="关闭xview" value="jmfe.xviewClose" @click="xviewClose" system="0" />
      <my-cell is-link title="获取设备信息" value="jmfe.getDeviceInfo" @click="getDeviceInfo" system="012" />
      <my-cell is-link title="获取APP版本" value="jmfe.getAppVersion" @click="getAppVersion" system="012" />
      <my-cell is-link title="版本比较" value="jmfe.versionCompare" @click="versionCompare" system="012" />
      <my-cell is-link title="保存图片" value="jmfe.saveImage" @click="saveImage" system="012" />
      <my-cell is-link title="配置导航栏" value="jmfe.configNavigationBar" @click="configNavigationBar" system="012" />
      <my-cell is-link title="添加预约" value="jmfe.addReminder" @click="addReminder" system="012" />
    </van-cell-group>
    <van-cell-group title="用户" inset>
      <my-cell is-link title="呼起登录面板" value="jmfe.toLogin" @click="toLogin" system="012" />
      <my-cell is-link title="检查App是否已经登录" value="jmfe.isJDAppLogin" @click="isJDAppLogin" system="012" />
      <my-cell is-link title="获取isvtoken" value="jmfe.requestIsvToken" @click="requestIsvToken" system="012" />
      <my-cell is-link title="呼起分享面板" value="jmfe.callSharePane" @click="callSharePane" system="012" />
    </van-cell-group>
    <van-cell-group title="跳转" inset>
      <my-cell is-link title="跳转购物车" value="jmfe.toMyCart" @click="toMyCart" system="012" />
      <my-cell is-link title="跳转商品详情" value="jmfe.toSku" @click="toSku" system="012" />
      <my-cell is-link title="跳转店铺详情" value="jmfe.toShop" @click="toShop" system="012" />
      <my-cell is-link title="跳转订单明细" value="评价用" @click="toOrderDetail" system="012" />
      <my-cell is-link title="跳转我的京豆" value="jmfe.toMyJd" @click="toMyJd" system="012" />
      <my-cell is-link title="跳转多种路径" value="jmfe.toAny" @click="toAny" system="012" />
      <my-cell is-link title="跳转店铺客服" value="jmfe.toCustomerService" @click="toCustomerService" system="01" />
      <my-cell is-link title="跳转我的优惠券" value="jmfe.toMyCoupon" @click="toMyCoupon" system="01" />
      <my-cell is-link title="跳转我的红包" value="jmfe.toMyRedbag" @click="toMyRedbag" system="01" />
    </van-cell-group>

    <van-cell-group title="调试鸿蒙" inset>
      <van-cell title="是否平板设备" value="缺少设备，无法验证" system="01" label="用来在平板访问活动的时候，使用m站策略" />
      <van-cell title="关闭xview" value="仅ios环境使用,无需适配" system="01" label="直播间关闭当前半屏" />
      <van-cell is-link title="跳转店铺客服" @click="toCustomerServiceForHarmony" system="01" label="window.jmfe.toAny('https://jdcs.m.jd.com/chat/index.action?venderId=739130&entry=m_shop&sceneval=2')" />
      <van-cell is-link title="跳转我的优惠券" @click="toMyCouponForHarmony" system="01" label="window.jmfe.toAny('https://my.m.jd.com/coupon/index.html')" />
      <van-cell is-link title="跳转我的红包" @click="toMyRedbagForHarmony" system="01" label="window.jmfe.toAny('https://my.m.jd.com/redpack/index.html')" />
    </van-cell-group>
  </div>
  <!-- 底部安全区 -->
  <div class="safe-area-bottom"></div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { showDialog, showToast } from 'vant';
import 'vant/es/dialog/style';
import DOMAIN, { exchangeSubStr } from '@/utils/platforms/domain';
import MyCell from './components/MyCell.vue';

// 是否平板设备
const checkTabletForHarmony = () => {
};

// 跳转店铺客服
const toCustomerServiceForHarmony = () => {
  window.jmfe.toAny(`https://jdcs.m.jd.com/chat/index.action?venderId=739130&entry=m_shop&sceneval=2`);
};

// 跳转我的优惠券
const toMyCouponForHarmony = () => {
  window.jmfe.toAny('https://my.m.jd.com/coupon/index.html');
};

// 跳转我的红包
const toMyRedbagForHarmony = () => {
  window.jmfe.toAny('https://my.m.jd.com/redpack/index.html');
};

// 跳转我的订单详细，评价用
const toOrderDetail = () => {
  const schema = window.jmfe.buildSchema({
    des: "orderDetail",
    isNew: true,
    category: "jump",
    sourceValue: "",
    sourceType: "",
    orderId: "313286064907"
  })
  window.jmfe.toAny(schema);
};

const jssdkVersion = computed(() => {
  return window.jmfe.VERSION;
});

interface ResponseType {
  status: string;
  data: string;
  msg: string;
}

const toLogin = () => {
  window.jmfe.toLogin();
};

const isJDAppLogin = () => {
  window.jmfe.isJDAppLogin()
    .then(({
      status,
      data,
      msg,
    }: ResponseType) => {
      if (status === '0' && data === '1') {
        showToast('harmony已登录');
      } else if (status === '0' && data === '0') {
        showToast('harmony未登录');
      } else if (status === '1' && data === '1') {
        showToast('android/ios已登录');
      } else if (status === '1' && data === '0') {
        showToast('android/ios未登录');
      }
    });
};

const requestIsvToken = () => {
  const url = exchangeSubStr(window.location.origin, DOMAIN.PROTECT, DOMAIN.COMMON);
  window.jmfe.requestIsvToken(url)
    .then(({
      status,
      data,
      msg,
    }: ResponseType) => {
      if (status === '0') {
        //data就是获取到的isv的token
        showToast('获取到的isv的token:' + data);
      } else {
        showToast('获取isv的token失败:' + msg);
      }
    });
};

const callSharePane = () => {
  window.location.href = 'https://lzkjdz-isv.isvjcloud.com/test/cc/custom/demo/share/';
};

const toAny = () => {
  window.jmfe.toAny('https://shop.m.jd.com/shop/home?venderId=1000002668');
};

const toCustomerService = () => {
  window.jmfe.toCustomerService({
    shopId: '12345',
  });
};

const toMyCoupon = () => {
  window.jmfe.toMyCoupon();
};

const toMyRedbag = () => {
  window.jmfe.toMyRedbag();
};

const xviewClose = () => {
  window.jmfe.xviewClose();
};

const checkTablet = () => {
  const isTablet = window.jmfe.isTablet();
  showToast('是否平板:' + isTablet);
};

const checkMobile = () => {
  const isMobile = window.jmfe.isMobile();
  showToast('是否移动设备:' + isMobile);
};

const getDeviceInfo = () => {
  console.log('获取设备信息');
  window.jmfe.getDeviceInfo()
    .then(({
      status,
      data,
      msg,
    }: ResponseType) => {
      if (status === '0') {
        //data就是获取到的设备信息
        showDialog({
          message: JSON.stringify(data, null, 2),
          messageAlign: 'left',
        });
      } else {
        showToast('设备信息失败:' + msg);
      }
    });
};

const checkIsApp = () => {
  const isApp = window.jmfe.isApp('jd');
  showToast('是否京东APP:' + isApp);
};

const checkIsIOS = () => {
  const isIOS = window.jmfe.isIOS();
  showToast('是否iOS:' + isIOS);
};

const toMyJd = () => {
  window.jmfe.toMyJd();
};

const getAppVersion = () => {
  const version = window.jmfe.getAppVersion('jd');
  showToast('APP版本:' + version);
};

const versionCompare = () => {
  const result = window.jmfe.versionCompare('4.0.5', '4.7.3');
  showToast('版本比较结果:' + result);
};

const checkIsWebview = () => {
  const isWebview = window.jmfe.isWebview();
  showToast('是否Webview:' + isWebview);
};

const toShop = () => {
  window.jmfe.toShop('734259');
};

const saveImage = () => {
  const params = {
    url: 'https://img12.360buyimg.com/imagetools/jfs/t1/121277/31/36863/4799/649ac6dcF08720d3a/d6829d73a1559224.jpg',
  };
  window.jmfe.saveImage(params)
    .then(({
      status,
      data,
      msg,
    }: ResponseType) => {
      if (status === '0') {
        //保存成功
        showToast('保存成功');
      } else {
        //保存失败
        showToast('保存失败:' + msg);
      }
    });
};

const toMyCart = () => {
  window.jmfe.toMyCart({
    extraOpenAppParams: {
      skuList: ['100033111647'],
    },
  });
};

const toSku = () => {
  window.jmfe.toSku('100122998761');
};

const checkIsAndroid = () => {
  const isAndroid = window.jmfe.isAndroid();
  showToast('是否Android:' + isAndroid);
};

const closeWebview = () => {
  window.jmfe.closeWebview();
};

const configNavigationBar = () => {
  const params = {
    tranParams: {
      backgroundColor: '#FF0000',
    },
  };
  console.log('configNavigationBar', params);
  window.jmfe.configNavigationBar(params)
    .then(({
      status,
      data,
      msg,
    }: ResponseType) => {
      console.log({
        status,
        data,
        msg,
      });
      if (status === '0' || status === '0000') {
        //设置成功
        showToast('设置成功');
      } else {
        showToast('设置失败：' + msg);
      }
    });
};

const addReminder = () => {
  window.jmfe
    .addReminder({
      id: 12345, //可以使用相关活动id、页面id、广告组id或广告id等
      tag: '预约标签',
      time: new Date().getTime(), //可以使用Date对象的getTime方法获取，例如：new Date().getTime()
      title: '预约标题',
      type: 'HUODONG',
      url: {
        des: 'productDetail',
        params: { skuId: '100122998761' },
      }, //支持h5页面地址或jump协议对象
    })
    .then(({ appointed }: { appointed: boolean }) => {
      if (appointed) {
        showToast('添加预约成功');
      } else {
        showToast('添加预约失败');
      }
    });
};
</script>

<style lang="scss">
body {
  padding: 0;
  margin: 0;
}
</style>

<style lang="scss" scoped>
.safe-area-bottom {
  height: 2rem;
}
</style>
