<template>
  <div>
    <van-cell v-bind="$attrs">
      <template #label>
        <div style="display: flex; gap: 3px">
          <van-tag  type="primary" v-if="props.system.includes('0')">IOS</van-tag>
          <van-tag  type="success" v-if="props.system.includes('1')">Android</van-tag>
          <van-tag  type="danger" v-if="props.system.includes('2')">Harmony</van-tag>
        </div>
      </template>
    </van-cell>
  </div>
</template>
<script setup lang="ts">
import { withDefaults, defineProps } from 'vue';

const props = withDefaults(defineProps<{
  system: string
}>(), {
  system: '', // 设置默认值为空字符串
});
</script>
