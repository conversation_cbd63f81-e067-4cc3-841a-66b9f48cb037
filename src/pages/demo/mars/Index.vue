<template>
  <img :src="imgUrl" />
  <br />
  <button @click="handleGen">合成图片</button>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

const img = 'https://petcare-consumer-qa.marschina.com/blobs/images/e10c070ca8ac94cdf9ecaae3f0a7bf8e.jpeg';
const imgUrl = ref<string>();

const handleGen = () => {
  // 使用canvas合成图片
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  if (!ctx) return;

  const img1 = new Image();
  img1.crossOrigin = 'anonymous'; // 设置跨域
  img1.src = img;
  img1.onload = () => {
    canvas.width = img1.width;
    canvas.height = img1.height;
    ctx.drawImage(img1, 0, 0);

    const img2 = new Image();
    img2.crossOrigin = 'anonymous'; // 设置跨域
    img2.src = img;
    img2.onload = () => {
      ctx.drawImage(img2, 0, 0);
      imgUrl.value = canvas.toDataURL('image/jpeg');
    };
  };
};
</script>

<style lang="scss">

</style>
