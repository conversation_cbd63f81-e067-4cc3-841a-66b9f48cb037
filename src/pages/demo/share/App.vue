<template>
  <div>
    <CellGroup title="设置分享渠道" />
    <CheckboxGroup
      v-model="channel" direction="horizontal" class="channels" @change="onChannelChange">
      <Checkbox name="Wxfriends">微信好友</Checkbox>
      <Checkbox name="Wxmoments">微信朋友圈</Checkbox>
      <Checkbox name="Sinaweibo">新浪微博</Checkbox>
      <Checkbox name="QQfriends">QQ好友</Checkbox>
      <Checkbox name="QQzone">QQ空间</Checkbox>
      <Checkbox name="QRCode">二维码分享</Checkbox>
      <Checkbox name="CopyURL">复制链接</Checkbox>
      <Checkbox name="CopyJDKey">复制京口令</Checkbox>
    </CheckboxGroup>
    <CellGroup title="分享内容设置">
      <Field v-model="shareConfig.title" label="标题" rows="1" autosize type="textarea" />
      <Field v-model="shareConfig.content" label="内容" rows="1" autosize type="textarea" />
      <Field v-model="shareConfig.imageUrl" label="图片" rows="1" autosize type="textarea" />
      <Field v-model="shareConfig.shareUrl" label="地址" rows="1" autosize type="textarea" />
    </CellGroup>
    <RadioGroup v-model="shareConfig.type">
      <CellGroup title="设置分享方式">
        <Cell v-for="(type, index) in shareTypeDict" :key="type" :title="type" clickable @click="setShareType(index)">
          <template #right-icon>
            <Radio :name="`${index}`" />
          </template>
        </Cell>
      </CellGroup>
    </RadioGroup>
    <div style="padding:20px;">
      <Row gutter="20">
        <Col span="12">
          <Button type="primary" block @click="handleSetHeaderShare">设置头部分享</Button>
        </Col>
        <Col span="12">
          <Button type="danger" block @click="handleCallShare">手动调用分享</Button>
        </Col>
      </Row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import {
  CellGroup, Cell, RadioGroup, Radio, showToast, Field, Button, Row, Col, Divider, Sticky, Checkbox, CheckboxGroup,
} from 'vant';
import { callShare, setHeaderShare } from '@/utils/platforms/share';

window.document.title = '测试页面';
const shareTypeDict = ['京口令分享', 'H5分享', '小程序分享'];
const channel = ref<string[]>(['Wxfriends', 'Wxmoments', 'Sinaweibo', 'QQfriends', 'QQzone', 'QRCode', 'CopyURL', 'CopyJDKey']);

const shareConfig = ref({
  type: '0',
  title: '测试分享标题',
  content: '测试分享内容',
  imageUrl: 'https://img10.360buyimg.com/imgzone/jfs/t1/210129/26/4714/187430/6163ec3bE539a9f29/69985ad74d67c5e6.jpg',
  shareUrl: window.location.pathname + window.location.hash + window.location.search,
  // Wxfriends // 微信好友
  // Wxmoments // 微信朋友圈
  // Sinaweibo // 新浪微博
  // QQfriends // QQ好友
  // QQzone // QQ空间
  channel: channel.value.join(','),
});

const setShareType = (type: number): void => {
  shareConfig.value.type = type.toString();
  console.log(`分享方式切换至：${shareTypeDict[type]}`);
  showToast(`分享方式切换至：${shareTypeDict[type]}`);
};

const setShareUrl = (url: string): void => {
  shareConfig.value.shareUrl = url;
  console.log(`分享url切换至：${url}`);
  showToast(`分享url切换至：${url}`);
};

const getConfig = () => {
  let shareUrl = `${shareConfig.value.shareUrl}`;
  if (shareConfig.value.shareUrl?.indexOf('https://') === -1) {
    shareUrl = `${window.location.origin}${shareConfig.value.shareUrl}`;
  }

  const config = {
    ...shareConfig.value,
    shareUrl,
  };
  console.log(config);
  return config;
};

const onChannelChange = (value: any): void => {
  shareConfig.value.channel = channel.value.join(',');
  console.log(`设置分享渠道为:${value}`);
  showToast(`设置分享渠道为:${value}`);
};

const handleSetHeaderShare = (): void => {
  setHeaderShare({
    ...getConfig(),
    afterShare: () => {
      console.log('设置头部分享 afterShare');
      showToast('设置头部分享 afterShare');
    },
    beforeShare: () => {
      console.log('设置头部分享 beforeShare');
      showToast('设置头部分享 beforeShare');
    },
  });
  showToast('设置头部分享');
};

const handleCallShare = async (): Promise<void> => {
  callShare({
    ...getConfig(),
    afterShare: () => {
      console.log('手动唤起分享 afterShare');
      showToast('手动唤起分享 afterShare');
    },
    beforeShare: () => {
      console.log('手动唤起分享 beforeShare');
      showToast('手动唤起分享 beforeShare');
    },
  });
  showToast('手动唤起分享');
};
</script>

<style lang="scss" scoped>
.channels {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  padding: 15px;
  gap: 20px;
}
</style>
