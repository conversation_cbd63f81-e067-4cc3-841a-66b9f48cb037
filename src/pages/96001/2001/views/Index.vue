<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv select-hover">
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/221554/37/2962/102923/61946cd8Ef358cd79/825d72a67d89b73c.png'" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <span v-if="furnish.disableShopName === 1" >{{ shopName }}</span>
        </div>
        <div>
          <div class="header-btn" :style="furnishStyles.headerBtn.value" @click="showRulePopup" v-click-track="'hdgz'">活动规则></div>
        </div>
      </div>
    </div>
    <div class="info">
      <div class="info-title">
        <img v-if="!isFinished" class="notFinished" src="//img10.360buyimg.com/imgzone/jfs/t1/279731/11/25270/8287/68071133Ffeac7b45/a9768461dc85ef44.png" alt="">
        <img v-else class="hasFinished" src="//img10.360buyimg.com/imgzone/jfs/t1/270436/14/26053/8424/68071133Fc452f2e6/59ec3cebf2d2ff27.png" alt="">
      </div>
      <div class="info-form">
        <van-form ref="formEl" @submit="onSubmit">
          <van-cell-group inset>
            <van-field v-if="showNameItem" v-model="userName" :disabled="isFinished" autocomplete="off" class="field" name="姓名" label="姓名：" maxlength="10" placeholder="请输入姓名" :rules="[{ required: true, message: '请填写姓名' }]" />
            <van-field v-if="showBirthdayItem" v-model="birthday" :disabled="isFinished" class="field" name="生日" label="生日：" readonly placeholder="点击选择生日" @click="changeDate" :rules="[{ required: true, message: '请填写生日' }]" />
            <van-field v-if="showPhoneItem" v-model="phoneNumber" :disabled="isFinished" autocomplete="off" class="field" name="电话" label="电话：" placeholder="请输入电话" maxlength="11" :rules="[{ validator: validatorPhone, message: validatorPhoneMessage }]" />
            <van-field
              v-if="showPhoneItem && !isFinished"
              v-model="verificationCode"
              :disabled="isFinished"
              autocomplete="off"
              class="field verification-code-field"
              name="验证码"
              label="验证码："
              placeholder="请输入短信验证码"
              maxlength="6"
              :rules="[{ validator: validatorVerificationCode, message: validatorVerificationCodeMessage }]"
            >
              <template #button>
                <div class="send-verification-btn" :class="{gray:isCounting}" v-click-track="'hqyzm'" @click="getVerificationCode()">
                  {{ buttonText }}
                </div>
              </template>
            </van-field>
            <div v-if="showGenderItem" class="gender-field">
              <div :class="isFinished ? 'hasFinished' : 'gender-label'">性别：</div>
              <van-radio-group v-model="gender" direction="horizontal" class="gender-radio-group" :disabled="isFinished" shape="dot">
                <van-radio name="男" checked-color="#000" value="男">男</van-radio>
                <van-radio name="女" checked-color="#000" value="女">女</van-radio>
              </van-radio-group>
            </div>
            <van-field v-if="showEmailItem" v-model="email" :disabled="isFinished" class="field" autocomplete="off" maxlength="30" name="邮箱" label="邮箱：" placeholder="请输入邮箱" :rules="[{ validator: validatorEmail, message: validatorEmailMessage }]" />
            <van-field v-if="showAreaItem" v-model="area" :disabled="isFinished" is-link readonly class="field" name="地址" label="选择地址：" placeholder="点击选择地址" @click="changeArea" :rules="[{ required: true, message: '请选择地址' }]" />
            <van-field v-if="showDetailAddressItem" v-model="detailAddress" :disabled="isFinished" autocomplete="off" class="field" maxlength="100" name="详细地址" label="详细地址：" placeholder="请输入详细地址" :rules="[{ required: true, message: '请填写详细地址' }]"/>
            <div v-for="(item, index) in itemList" :key="index">
              <van-field v-if="item.type === 1" class="field" :disabled="isFinished" :name="item.title" :label="item.title" :placeholder="`请填写${item.title}`" v-model="item.content" maxlength="34" :rules="[{ required: true, message: `请填写${item.title}` }]" />
            </div>
            <div class="check-box" v-if="!isFinished">
              <div class="termCheckbox">
                <van-checkbox v-model="termCheckbox" checked-color="#000" icon-size="12px" :disabled="isFinished"></van-checkbox>
                <p>
                  我已阅读并同意馥蕾诗按照<span style="color: #1c2779;" v-click-track="'grxxclgz'" @click="privacyPolicy = true">《个人信息处理规则》</span>所述处理我的个人信息。
                </p>
              </div>
              <div class="termCheckbox">
                <div style="width: 0.5rem; height: 0.5rem"></div>
                <p>同一用户只可完善一次，为了保障您的会员权益，<span style="color: #1c2779;">请填写正确的生日信息，填写后不可修改。</span></p>
              </div>
            </div>
            <div v-if="!isFinished" v-click-track="'qrtj'" @click="checkForm" class="submit"></div>
            <div v-else v-click-track="'jdkk'" @click="gotoShopPage(baseInfo.shopId)" class="hasSubmited"></div>
          </van-cell-group>
        </van-form>
      </div>
    </div>
  </div>
  <!--  时间选择-->
  <van-popup v-model:show="showDatePicker" position="bottom">
    <van-date-picker v-model="pickerDate" @confirm="confirmBirthday" @cancel="showDatePicker = false" :max-date="new Date()" :minDate="new Date(new Date().getFullYear() - 100, new Date().getMonth(), new Date().getDate())" />
  </van-popup>
  <!--  地址选择-->
  <van-popup v-model:show="showArea" position="bottom">
    <van-area :area-list="areaList" @confirm="confirmArea" @cancel="showArea = false" />
  </van-popup>
  <!-- 入会弹窗 -->
  <VanPopup teleport="body" v-model:show="showJoin">
    <JoinMember @close="showJoin = false"></JoinMember>
  </VanPopup>
  <!-- 规则弹窗 -->
  <VanPopup teleport="body" v-model:show="showRule">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
  <!-- 隐私政策 -->
  <VanPopup teleport="body" v-model:show="privacyPolicy">
    <PrivacyPolicy :rule="ruleTest" @close="privacyPolicy = false"></PrivacyPolicy>
  </VanPopup>
  <!-- 中奖弹窗 -->
  <VanPopup teleport="body" v-model:show="showAward">
    <AwardPopup :prize="award" @close="showAward = false" @saveAddress="toSaveAddress" @showCardNum="showCardNum" @savePhone="showSavePhone"></AwardPopup>
  </VanPopup>
</template>

<script setup lang="ts">
import { ref, inject, computed } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import Swiper, { Autoplay } from 'swiper';
import RulePopup from '../components/RulePopup.vue';
import JoinMember from '../components/JoinMember.vue';
import AwardPopup from '../components/AwardPopup.vue';
import PrivacyPolicy from '../components/PrivacyPolicy.vue';
import { DecoData } from '@/types/DecoData';
import { httpRequest } from '@/utils/service';
import { BaseInfo } from '@/types/BaseInfo';
import { lzReportClick } from '@/utils/trackEvent/lzReport';
import { areaList } from '@vant/area-data';
import { gotoShopPage } from '@/utils/platforms/jump';

Swiper.use([Autoplay]);

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;

const formEl = ref();
const termCheckbox = ref(false);
const shopName = ref(baseInfo.shopName);

const showJoin = ref(false);
const showRule = ref(false);
const privacyPolicy = ref(false);
const ruleTest = ref('');
const prizeInfo = ref({
  prizeName: '',
  prizeImg: '',
  activityPrizeId: '',
});

// 定时器对象
const timer = ref();
const countdown = ref(0);
// 计算属性：是否正在倒计时
const isCounting = computed(() => countdown.value > 0);
// 计算属性：按钮文字
const buttonText = computed(() => (isCounting.value ? `${countdown.value}s` : '获取验证码'));

// 开始倒计时
const startCountdown = () => {
  countdown.value = 60;
  timer.value = setInterval(() => {
    countdown.value -= 1;
    if (countdown.value <= 0) {
      clearInterval(timer.value);
    }
  }, 1000);
};

// 检查活动时间
const checkActTime = () => {
  const now = new Date().getTime();
  if (now < startTime.value) {
    showToast('活动未开始');
    return false;
  }
  if (now > endTime.value) {
    showToast('活动已结束');
    return false;
  }
  return true;
};

// 发送验证码请求
const getVerificationCode = async () => {
  if (!checkActTime()) {
    return;
  }
  // 校验电话号码
  console.log('校验号码');
  const checkPhone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  if (!phoneNumber.value) {
    showToast('请输入您的手机号码');
    return;
  } if (!checkPhone.test(phoneNumber.value)) {
    showToast('请输入正确的手机号码');
    return;
  }
  // 检查是否正在倒计时
  if (isCounting.value) return;
  // 发送验证码
  try {
    showLoadingToast({
      message: '发送中...',
      forbidClick: true,
      duration: 0,
    });
    await httpRequest.post('/common/sendIdentifyCode', { phone: phoneNumber.value });
    closeToast();
    startCountdown();
    showToast('验证码发送成功');
  } catch (error) {
    closeToast();
    showToast(error.message);
  }
};

const isFinished = ref(false);

// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};

const userName = ref('');
const phoneNumber = ref('');
const email = ref('');
const verificationCode = ref('');

// 生日
const birthday = ref();
const today = new Date();
const pickerDate = ref([today.getFullYear(), today.getMonth() + 1, today.getDate()]);
const showDatePicker = ref(false);
const changeDate = () => {
  if (!isFinished.value) {
    showDatePicker.value = true;
  }
};
const confirmBirthday = ({ date }: any) => {
  date = pickerDate.value;
  birthday.value = date.join('/');
  showDatePicker.value = false;
};

// 性别
const gender = ref('');

// 住址修改
const showArea = ref(false);
const area = ref('');
const detailAddress = ref('');
const changeArea = () => {
  if (!isFinished.value) {
    showArea.value = true;
  }
};
const confirmArea = ({ selectedOptions }: any) => {
  showArea.value = false;
  area.value = selectedOptions.map((item: any) => item.text).join('/');
};

const showNameItem = ref(false);
const showBirthdayItem = ref(false);
const showPhoneItem = ref(false);
const showGenderItem = ref(false);
const showEmailItem = ref(false);
const showAreaItem = ref(false);
const showDetailAddressItem = ref(false);

// 获取填写的信息列表
const itemList: any = ref([]);
const allDataInfo: any = ref([]);

const getItem = async () => {
  try {
    const { data } = await httpRequest.post('/96001/getItem');
    allDataInfo.value = data.allInfo;
    itemList.value = [];
    if (data.status === 1) {
      isFinished.value = true;
      data.allInfo.forEach((item: any) => {
        if (item.title === '姓名') {
          userName.value = item.content;
        }
        if (item.title === '生日') {
          birthday.value = item.content;
        }
        if (item.title === '电话') {
          phoneNumber.value = item.content;
        }
        if (item.title === '性别') {
          gender.value = item.content;
        }
        if (item.title === '邮箱') {
          email.value = item.content;
        }
        if (item.title === '地址') {
          area.value = item.content;
        }
        if (item.title === '详细地址') {
          detailAddress.value = item.content;
        }
      });
    }
    data.allInfo.forEach((item: any) => {
      if (item.type === 0) {
        if (item.title === '姓名') {
          showNameItem.value = true;
        }
        if (item.title === '生日') {
          showBirthdayItem.value = true;
          if (item.content) {
            birthday.value = item.content;
          }
        }
        if (item.title === '电话') {
          showPhoneItem.value = true;
          allDataInfo.value.push({
            content: '',
            num: '99',
            title: '验证码',
            type: 0,
          });
        }
        if (item.title === '性别') {
          showGenderItem.value = true;
        }
        if (item.title === '邮箱') {
          showEmailItem.value = true;
        }
        if (item.title === '地址') {
          showAreaItem.value = true;
        }
        if (item.title === '详细地址') {
          showDetailAddressItem.value = true;
        }
      } else {
        itemList.value.push(item);
      }
    });
  } catch (error) {
    console.error(error);
  }
};

const isStart = ref(false);
const startTime = ref();
const endTime = ref();
const getTime = () => {
  startTime.value = new Date(baseInfo.startTime).getTime();
  endTime.value = new Date(baseInfo.endTime).getTime();
  const now = new Date().getTime();
  if (now > startTime.value) {
    isStart.value = true;
  }
  if (now < startTime.value) {
    isStart.value = false;
  }
};

const reloadPage = () => {
  if (baseInfo.status === 3) {
    return;
  }
  window.location.reload();
};

const showAward = ref(false);
const award = ref({
  prizeType: 1,
  prizeName: '',
  showImg: '',
  result: '',
  activityPrizeId: '',
  userPrizeId: '',
});

const status = ref(0);
// 获取奖品信息
const getPrize = async () => {
  try {
    const { data } = await httpRequest.post('/96001/getPrize');
    prizeInfo.value.prizeName = data[0].prizeName;
    prizeInfo.value.prizeImg = data[0].prizeImg;
    prizeInfo.value.activityPrizeId = data[0].id;
    status.value = data[0].status;
  } catch (error) {
    console.error(error);
  }
};

const onSubmit = async () => {
  lzReportClick('tjxx');
  try {
    if (isFinished.value) {
      showToast('您已经提交过信息了哦~');
    } else {
      allDataInfo.value.forEach((item: any) => {
        if (item.title === '姓名') {
          item.content = userName.value;
        }
        if (item.title === '生日') {
          item.content = birthday.value;
        }
        if (item.title === '电话') {
          item.content = phoneNumber.value;
        }
        if (item.title === '验证码') {
          item.content = verificationCode.value;
        }
        if (item.title === '邮箱') {
          item.content = email.value;
        }
        if (item.title === '性别') {
          item.content = gender.value;
        }
        if (item.title === '地址') {
          item.content = area.value;
        }
        if (item.title === '详细地址') {
          item.content = detailAddress.value;
        }
      });
      await httpRequest.post('/96001/addInfo', {
        allInfo: allDataInfo.value,
      });
      showToast('提交成功');
      await getPrize();
      return true;
    }
  } catch (error) {
    showToast(error.message);
    return false;
  }
  console.log('submit');
};

const confirmValue = ref(false);

const validatorPhoneMessage = (val: string) => {
  const checkPhone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  if (!val) {
    return '请输入电话';
  }
  if (!checkPhone.test(val)) {
    return '请输入正确的电话';
  }
  return '';
};
const validatorPhone = (val: string) => {
  const checkPhone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  if (!val) {
    return false;
  }
  if (!checkPhone.test(val)) {
    return false;
  }
  return true;
};

const validatorEmailMessage = (val: string) => {
  const checkEmail = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+$/;
  if (!val) {
    return '请输入邮箱';
  }
  if (!checkEmail.test(val)) {
    return '请输入正确的邮箱';
  }
  return '';
};
const validatorEmail = (val: string) => {
  const checkEmail = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+$/;
  if (!val) {
    return false;
  }
  if (!checkEmail.test(val)) {
    return false;
  }
  return true;
};

const validatorVerificationCodeMessage = (val: string) => {
  const checkCode = /^\d{6}$/;
  if (!val) {
    return '请输入验证码';
  }
  if (!checkCode.test(val)) {
    return '验证码必须为6位数字';
  }
  return '';
};

const validatorVerificationCode = (val: string) => {
  const checkCode = /^\d{6}$/;
  if (!val) {
    return false;
  }
  if (!checkCode.test(val)) {
    return false;
  }
  return true;
};
// 检查表单
const checkForm = async () => {
  const isFormValid = true; // 默认校验通过
  await formEl.value.validate();
  if (isFinished.value) {
    showToast('您已经提交过信息了哦~');
    confirmValue.value = false;
  } else if (isFormValid) {
    if (termCheckbox.value) {
      if (await onSubmit()) {
        isFinished.value = true;
      };
    } else {
      showToast('请您仔细阅读《个人信息处理规则》');
    }
  }
};

const init = async () => {
  if (baseInfo.thresholdResponseList.length && baseInfo.thresholdResponseList[0].thresholdCode === 4) {
    showJoin.value = true;
    console.log('您不是会员');
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    getTime();
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getPrize(), getItem()]);
    closeToast();
    if (baseInfo.status === 1) {
      setTimeout(() => {
        showToast('活动未开始');
      }, 1000);
      closeToast();
      return;
    }
    if (baseInfo.status === 3) {
      setTimeout(() => {
        showToast('活动已结束');
      }, 1000);
      closeToast();
      return;
    }
  } catch (error) {
    closeToast();
  }
};

init();
</script>

<style scoped lang="scss">
.bg {
  min-height: 100vh;
}

.header-kv {
  position: relative;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0.3rem 0;
    display: flex;
    justify-content: space-between;
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    padding: 0.3rem 0.2rem 0;
    line-height: 0.44rem;
    margin-bottom: 0.1rem;
    font-size: 0.38rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}

.rollBall {
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/92078/22/28937/413201/62a94296E05b428d0/f9b47375e2b2b27f.png');
  background-repeat: no-repeat;
  background-size: 100%;
  width: 7.5rem;
  height: 10.7rem;
  position: relative;
  margin: 0 auto;

  .content {
    position: absolute;
    left: 3.35rem;
    top: 7.8rem;
    width: 3rem;

    .start {
      width: 2.5rem;
      margin: 0 auto;
    }

    .small-big {
      width: 0.89rem;
      margin-left: 1.8rem;
      animation: smallBig 1s infinite;
      animation-timing-function: linear;
      animation-direction: alternate;
    }
  }

  .award {
    position: absolute;
    top: 8rem;
    left: 1.5rem;
    animation: moveTop 1s;

    img {
      width: 1.5rem;
    }
  }

  @keyframes moveTop {
    0% {
      margin-top: -50px;
      opacity: 0;
    }
    30% {
      margin-top: -30px;
      opacity: 0;
    }
    50% {
      margin-top: -10px;
      opacity: 0.8;
    }
    100% {
      margin-top: 0px;
      opacity: 1;
    }
  }
  @keyframes smallBig {
    0% {
      transform: scale(1);
    }
    100% {
      transform: scale(1.3);
    }
  }
}

.draws-num {
  width: 3.02rem;
  height: 0.65rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/11238/24/17642/2162/62a94ad2Ec9fa1d93/307b172b89523acc.png);
  background-size: 100%;
  background-repeat: no-repeat;
  text-align: center;
  font-size: 0.22rem;
  line-height: 0.55rem;
}

.draw-btn {
  width: 6.9rem;
  margin: 0.4rem auto;

  .count-down {
    position: relative;
    top: -0.56rem;
    left: 0.7rem;
    width: 6rem;
    font-size: 0.25rem;
    color: #f2270c;

    .contentSpan {
      margin-left: 0.39rem;
      display: flex;
      width: 3rem;
      position: absolute;
      top: -0.06rem;
      left: 2.32rem;

      .acblockStyleStyle {
        width: 0.4rem;
        height: 0.44rem;
        color: rgb(242, 39, 12);
        background: rgb(255, 255, 255);
        border-radius: 0.05rem;
        display: flex;
        font-size: 0.25rem;
        justify-content: center;
        align-items: center;
      }

      span {
        width: 0.4rem;
        height: 0.44rem;
        color: rgb(255, 255, 255);
        display: flex;
        font-size: 0.25rem;
        justify-content: center;
        align-items: center;
      }
    }
  }

  img {
    width: 100%;
  }
}

.prizes {
  margin: 0.2rem;
  border-radius: 0.3rem;
  background: rgb(255, 255, 255);

  .gift-logo {
    position: absolute;
    background-repeat: round;
    width: 0.96rem;
    height: 0.96rem;
  }

  .gift-title {
    text-align: center;
    font-weight: bold;
    display: flex;
    margin: 0 1.4rem 0 1.4rem;
    font-size: 0.3rem;
    padding-top: 0.21rem;
    span {
      line-height: 0.5rem;
      margin: 0 0.2rem 0 0.2rem;
    }
    img {
      width: 0.4rem;
      height: 0.4rem;
    }
  }

  .gift-show {
    margin-left: 0.9rem;
    margin-top: 0.32rem;
    margin-right: 0.26rem;
    padding-bottom: 0.22rem;
    display: -webkit-box;
    display: flex;
    -webkit-box-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    font-size: 0.25rem;
    align-items: center;

    .gift-img {
      width: 1.2rem;
      border: 0.02rem solid gray;
      border-radius: 0.16rem;
      .imgs {
        width: 1.2rem;
        height: auto;
        border-radius: 0.16rem;
        margin: 0 auto;
      }
    }

    .gift-info {
      display: flex;
      -webkit-box-align: end;
      align-items: flex-end;
    }
    /*领取*/
    .get-prize-btn1 {
      width: 1.89rem;
      height: 0.63rem;
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/185734/13/27845/3192/63203caeEb9e3d409/b64287e42189ab26.png);
      background-size: 100%;
    }
    /*已领取*/
    .get-prize-btn2 {
      width: 1.89rem;
      height: 0.63rem;
      background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/54384/28/22030/2967/63203caeEc771438d/5f45ae078d5aa22e.png);
      background-size: 100%;
    }
    /*已领光*/
    .get-prize-btn3 {
      width: 1.89rem;
      height: 0.63rem;
      background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/49848/8/21925/2895/63203caeE468bfb73/f45a47792b2ba7aa.png);
      background-size: 100%;
    }
    /*未填写*/
    .get-prize-btn4 {
      width: 1.89rem;
      height: 0.63rem;
      background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/186680/40/42290/2521/6556d9e2F407f6217/0e14dc897424f9e3.png);
      background-size: 100%;
    }
  }
}

.info {
  width: 7.18rem;
  height: auto;
  margin: 0.6rem auto 0 auto;
  position: relative;
  top: -2rem;
  z-index: 1;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/280331/10/24426/4313/680852d1Fe06bc0ac/c08ddb74d9f45986.png);
  padding-bottom: 0.5rem;

  .info-title{
    width: 100%;
    padding: 0.35rem;
    .notFinished{
      width: 4.02rem;
      margin: 0 auto;
    }
    .hasFinished{
      width: 3.56rem;
      margin: 0 auto;
    }
  }

  img {
    width: 6.9rem;
  }

  .info-form {
    width: 6.9rem;
    height: auto;
    font-size: 5rem;
    border-radius: 0 0 0.3rem 0.3rem;
    //background-color: #ffffff;
    .field {
      font-size: 0.25rem;
      //padding-left: 0;
      --van-field-label-width: 2rem;
    }

    .verification-code-field {
      :deep(.van-field__button) {
        padding-left: 0.2rem;
      }
    }

    .verification-btn {
      width: 2rem;
      height: 0.6rem;
      line-height: 0.6rem;
      text-align: center;
      font-size: 0.24rem;
      color: #fff;
      background-color: #000;
      border-radius: 0.1rem;

      &.disabled {
        background-color: #ccc;
        color: #666;
      }
    }
    .gender-field {
      display: flex;
      padding: 0.2rem 0;
      border-bottom: 1px solid #000;
      font-size: 0.25rem;
      .gender-label {
        width: 2rem;
      }
      .hasFinished{
        width: 2rem;
        color:#c8c9cc;
      }
      .gender-radio-group {
        flex: 1;
        display: flex;
        align-items: center;
        .van-radio {
          margin-right: 0.5rem;
        }
      }
    }
    .check-box {
      font-size: 0.23rem;
      padding: 0.5rem 0 0;
      .termCheckbox {
        display: flex;
        justify-content: flex-start;
        align-items: start;
        padding-bottom: 0.15rem;
        .van-checkbox {
          width: 0.5rem;
          height: 0.5rem;
        }
        .underline {
          text-decoration: underline;
        }
      }
    }
  }
}
.send-verification-btn {
  width: 1.5rem;
  height: 0.587rem;
  line-height: 0.61rem;
  text-align: center;
  font-size: 0.25rem;
  color: #000;
  position: absolute;
  right: -0.01rem;
  top: -0.01rem;
  /* border: 0.01rem solid #000; */
  border-radius: 0.3rem;
  line-height: 0.58rem;
  &.gray {
    opacity: 0.6;
    pointer-events: none;
  }
}
.submit {
  width: 3.03rem;
  height: 0.69rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/279865/34/24619/13731/68071133Fdb4e52b3/38616bdfbebce81f.png);
  background-size: 100% 100%;
  margin: 0.2rem auto 0.2rem auto;
}
.hasSubmited {
  width: 5.29rem;
  height: 0.69rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/282063/7/24841/19124/68071133Fd535e06a/2b8013f043321070.png);
  background-size: 100% 100%;
  margin: 1rem auto 0.2rem auto;
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}

.van-cell {
  border-bottom: 1px solid #000;
}
.van-cell-group--inset {
  border-radius: 0;
}
.info-form {
  .van-cell-group {
    background: transparent;
    margin: 0 0.8rem;
  }
  .van-cell {
    background: transparent;
    padding: 0.2rem 0;
  }
}
.van-field__label {
  width: 1.3rem;
}
.van-radio__icon {
  height: auto;
}
.van-radio__icon--dot{
  width: 0.2rem;
  height: 0.2rem;
}
.van-radio__icon--checked.van-radio__icon--dot{
  width: 0.2rem;
  height: 0.2rem;
  border-color: #000;
}
.van-radio__icon--checked.van-radio__icon--dot .van-radio__icon--dot__icon {
  width: 0.1rem;
  height: 0.1rem;
  background: #000;
  border-color: #000;
}
</style>
