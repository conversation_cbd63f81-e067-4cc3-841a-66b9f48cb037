import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { init, initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
const _decoData = {
  "actBg": "//img10.360buyimg.com/imgzone/jfs/t1/277635/4/25826/81576/68071130F76120b68/7fb956c5c297fc7f.png",
  "actBgColor": "#000",
  "shopNameColor": "#000",
  "btnColor": "#fff",
  "prizeListImg": "//img10.360buyimg.com/imgzone/jfs/t1/278189/32/25742/2679/68071132Fcabcf0bf/496fbe012d46273e.png",
  "mpImg": "https://img10.360buyimg.com/imgzone/jfs/t1/109384/38/24540/296091/62296aeeE2d9a0809/4bf24ebc30b9cd93.png",
  "cmdImg": "https://img10.360buyimg.com/imgzone/jfs/t1/109384/38/24540/296091/62296aeeE2d9a0809/4bf24ebc30b9cd93.png",
  "h5Img": "https://img10.360buyimg.com/imgzone/jfs/t1/109384/38/24540/296091/62296aeeE2d9a0809/4bf24ebc30b9cd93.png"
};
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '完善信息有礼';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  // app.provide('decoData', decoData);
  app.provide('decoData', _decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
