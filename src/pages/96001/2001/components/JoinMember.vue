<template>
  <div class="join-bg">
    <div class="toJoin" v-click-track="'ljrh'" @click="openCard"></div>
  </div>
</template>

<script lang="ts" setup>
import {inject} from 'vue';
import {BaseInfo} from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({
  rule: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['close']);

const openCard = () => {
  window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}&isJoin=1`)}`;
};

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.join-bg {
  border-radius: 0.2rem 0.2rem 0 0;
  width: 6.5rem;
  height: 8rem;
  margin: 0 auto;
  text-align: center;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/275083/35/26509/38096/68088fbbFa87ed3ca/a62ff6df213b1a56.png);
  background-repeat: no-repeat;
  background-size: 100%;
  padding: 6.8rem 0.5rem 0;

  .toJoin{
    width: 2.8rem;
    height: 0.7rem;
    border-radius: 0.1rem;
    margin: 0.2rem auto;
    //background: #fff;
  }
}
</style>
