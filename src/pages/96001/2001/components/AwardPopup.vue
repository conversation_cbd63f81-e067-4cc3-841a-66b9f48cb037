<template>
  <div class="bk" v-if="prize.prizeType !== 0">
    <img v-if="prize.prizeType === 2" src="//img10.360buyimg.com/imgzone/jfs/t1/273038/18/26475/35633/680846ddF0818c079/b4900a03178a2442.png" alt="" class="prize-img" />
    <img v-else :src="prize.showImg" alt="" class="prize-img" />
    <div class="content">
      <p class="p1">恭喜您,领取成功!</p>
      <div>
        <p class="p3" v-if="prize.prizeType === 2">京豆已放到您的账户中 京东-我的-京豆 中查看</p>
      </div>
      <div class="btn-list">
        <div class="btn" @click="close"></div>
      </div>
    </div>
  </div>
  <div class="thanks-join" v-else>
    <div class="close" @click="close"></div>
    <div class="btn" @click="gotoShopPage(baseInfo.shopId)">进店逛逛</div>
  </div>
</template>

<script lang="ts" setup>
import { BaseInfo } from '@/types/BaseInfo';
import { gotoShopPage } from '@/utils/platforms/jump';
import { PropType, inject } from 'vue';

const baseInfo = inject('baseInfo') as BaseInfo;

interface PrizeType {
  prizeType: number;
  prizeName: string;
  showImg: string;
  result: any;
  activityPrizeId: string;
  userPrizeId: string;
}
const props = defineProps({
  prize: {
    type: Object as PropType<PrizeType>,
    required: true,
  },
});

const emits = defineEmits(['close', 'saveAddress', 'showCardNum', 'savePhone']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.bk {
  height: 8rem;
  width: 6.5rem;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/280570/7/22819/10663/68076bacFe12191c1/3178909e22989997.png);
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 1.9rem;
  .prize-img {
    width: 4.48rem;
    margin: 0 auto;
  }
  .content {
    width: 5.6rem;
    height: 3.5rem;
    border-radius: 0.2rem;
    margin: 0.2rem auto 0;
    padding: 0.3rem;
    .p1 {
      display: block;
      color: #262626;
      font-size: 0.45rem;
      font-weight: 500;
      margin: 0 auto;
      text-align: center;
      color:#393939;
    }
    .p3 {
      width: 3rem;
      font-size: 0.25rem;
      color: #393939;
      display: block;
      text-align: center;
      height: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
    }
    .btn-list {
      display: flex;
      justify-content: space-between;
      .btn {
        width: 2.4rem;
        height: 0.9rem;
        line-height: 0.9rem;
        text-align: center;
        color: white;
        font-size: 0.3rem;
        border-radius: 0.1rem;
      }
    }
  }
}
.thanks-join {
  width: 6rem;
  height: 6.3rem;
  position: relative;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/192914/3/22012/24033/623985b9E8508c48b/019e54628504b2dc.png);
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 4.5rem;
  .close {
    height: 0.24rem;
    width: 0.24rem;
    position: absolute;
    right: 0.34rem;
    top: 0.34rem;
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/143521/26/18654/387/5fd9e706E8f1594e3/ae5cc06440559585.png) no-repeat;
    background-size: 100% 100%;
  }
  .btn {
    display: block;
    margin: 0 auto;
    width: 4rem;
    height: 0.76rem;
    line-height: 0.76rem;
    color: #fff;
    font-size: 0.3rem;
    border-radius: 0.38rem;
    text-align: center;
    background-color: rgb(201, 0, 26);
  }
}
</style>
