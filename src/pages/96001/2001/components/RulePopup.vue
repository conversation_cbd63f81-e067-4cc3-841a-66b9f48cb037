<template>
  <div class="rule-bk">
    <div class="content" v-html="rule"></div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps(['rule']);

const emits = defineEmits(['close']);

const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.rule-bk {
  border-radius: 0.2rem 0.2rem 0 0;
  width: 6.5rem;
  height: 8rem;
  margin: 0 auto;
  background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/271922/11/25100/10365/68076972F367edadb/eaa83e2ec0caa649.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding: 1.9rem 0.5rem 0;

  .content {
    height: 5.6rem;
    border: 0.3rem solid transparent;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
    &::-webkit-scrollbar {
      display: none;
    }
  }
}
</style>
