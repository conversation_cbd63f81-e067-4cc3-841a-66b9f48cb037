import { computed, reactive } from 'vue';

export const furnish = reactive({
  actBg: '', // 主页背景图
  actBgColor: '', // 主页背景色
  shopNameColor: '', // 店铺名称颜色
  btnColor: '', // 按钮字体颜色
  finishInfo: '', // 完善信息题目
  prizeListImg: '',
  disableShopName: 0,
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
}));

const shopNameColor = computed(() => ({
  color: furnish.shopNameColor ?? '',
}));

const headerBtn = computed(() => ({
  color: furnish.btnColor ?? '',
}));

export default {
  pageBg,
  shopNameColor,
  headerBtn,
};
