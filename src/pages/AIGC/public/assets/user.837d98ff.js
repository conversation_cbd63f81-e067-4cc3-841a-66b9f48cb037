import{e,_ as a,A as t,B as o,a0 as s,f as c,s as n}from"./index-b897ff36.js";import{a as i}from"./future.ea6e4888.js";import{c as d}from"./init.6dfa4376.js";let r="/babyApi";var l=!1;const u=function(t){return new Promise(((o,s)=>{l||(l=!0,setTimeout((async()=>{a({url:"/test/cc/custom/api/1000002668/aigc/dz/getTokenNew",method:"get",header:{"Content-Type":"application/json","Pin-Token":e(d.LZ_PIN_TOKEN)||""},data:{channelId:i(c.state.channel).channelId,channelSecret:i(c.state.channel).channelSecret},success(e){const a=e;0==a.data.data.code&&(n("ph_access_token",a.data.data.data.token),l=!1,o(a))},fail(e){l=!1,s(e)}})}),t||0))}))},m=async c=>{e("ph_access_token")||l||"/zhipu/common/login"==c.url||await u(1e3),r="occ_api"==c.urlType?"/occApi":"/test/cc/custom/api";let n=c.url,i=c.method||"get",p=c.data||{},h={"X-Access-Token":e("ph_access_token")||"","Content-Type":"application/json","Pin-Token":e(d.LZ_PIN_TOKEN)||""};return new Promise(((d,g)=>{setTimeout((()=>{a({url:r+n,method:i,header:h,timeout:6e5,data:{accessToken:e("ph_access_token")||"",...p},success:async e=>{const a=e;if(200==a.statusCode)if(20==a.data.code||21==a.data.code||401==a.data.code){await u();const e=await m(c);d(e)}else console.log(a.data),"/zhipu/common/login"==c.url||"/1000002668/aigc/dz/getOpenId"==c.url||"/common/getActivityBase"==c.url?(console.log(1111),d(a.data)):(console.log(2222),d(a.data.data));else switch(a.statusCode){case 401:case 404:console.log(a)}},fail(e){-1!==e.errMsg.indexOf("request:fail")?t({title:"网络异常",icon:"error",duration:2e3}):t({title:"未知异常",duration:2e3}),g(e)},complete(){o(),s()}})}),l?1e3:0)}))},p=e=>m({url:"/zhipu/common/login",method:"post",data:e}),h=e=>m({url:"/1000002668/aigc/dz/getOpenId",method:"post",data:e}),g=e=>m({url:"/1000002668/aigc/dz/memberQuery",method:"get",data:e}),_=e=>m({url:"/1000002668/aigc/dz/createMember",method:"post",data:e});export{g as a,h as g,p as l,_ as r};
