<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv">
      <img :src="furnish.actBg ?? 'https://img10.360buyimg.com/imgzone/jfs/t1/221554/37/2962/102923/61946cd8Ef358cd79/825d72a67d89b73c.png'" alt="" class="kv-img" />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <!--          {{ shopName }}-->
        </div>
        <div>
          <div class="header-btn" v-click-track="'hdgz'" :style="furnishStyles.headerBtnRules.value" @click="showRulePopup" />
          <div class="header-btn" v-click-track="'wdjp'" :style="furnishStyles.headerBtnMyPrizes.value" @click="showMyPrizePop" />
        </div>
      </div>
    </div>
    <div class="hotZoneBox">
      <img class="hotZone" :src="furnish.prizeBg" alt="" />
      <div class="prizeBox">
        <div class="qualificationBg">
          <div class="title" :style="furnishStyles.isAccordColor.value">尊敬的伊利用户：</div>
          <div class="text" :class="hasOrdBefore ? 'text-top' : ''" :style="furnishStyles.isAccordColor.value">
            {{ text }}
          </div>
          <div class="toBuyBtn" @click="buyNow" />
        </div>
        <div class="orderTipsText" :style="furnishStyles.orderLimitTextColor.value">
          <!--          <div>*{{dayjs(oldOrderStartTime).format('YYYY年MM月DD日 HH:mm:ss')}}-{{dayjs(oldOrderEndTime).format('YYYY年MM月DD日 HH:mm:ss')}}在伊利官方旗舰店有已完成的订单 </div>-->
          <div>*{{ dayjs(oldOrderStartTime).format('YYYY年MM月DD日') }}-{{ dayjs(oldOrderEndTime).format('YYYY年MM月DD日') }}在{{ shopName }}有已完成的订单</div>
          <div>
            *{{ dayjs(activityDataInfo.newOrderStartTime).format('YYYY年MM月DD日') }}-{{ dayjs(activityDataInfo.newOrderEndTime).format('YYYY年MM月DD日') }}{{ activityDataInfo.orderStrokeCount === 1 ? `单笔订单` : `多笔订单` }}购买{{ orderSkuisExposure === 0 ? '全店' : '指定' }}商品，实付款<span style="font-size: 0.26rem; font-weight: bolder">满{{ orderRestrainAmount }}元</span>，订单完成24小时候更新进度。
          </div>
          <div>*订单可能存在延迟，请订单完成48h来领取权益</div>
        </div>
        <div class="choosePrizes">
          <div class="choosePrizesBox">
            <div class="list-view" v-for="(item, index) in multiplePrizeList" :key="index">
              <div class="itemBg" :style="furnishStyles.prizeItemBg.value">
                <div class="equity_name" :style="furnishStyles.prizeItemTitleColor.value">
                  {{ item.prizeName }}
                </div>
                <img class="equity_img" :src="item.prizeImg ? item.prizeImg : '//img10.360buyimg.com/imgzone/jfs/t1/176585/24/10488/6916/60a4cb50E562734ab/f9ab956ec09a4146.png'" alt="" />
                <div class="btm-info">
                  <div class="equity_num" :style="furnishStyles.priceColor.value">{{ item.remainCount }}</div>
                  <div v-if="item.status === 1" class="equity_btn" v-click-track="'ljlq-qy'" @click="getPrize(item)" :style="furnishStyles.getPrizeBtn.value">点击领取</div>
                  <div v-else-if="item.status === 2" class="equity_btn" v-click-track="'ljlq-qy'" @click="getPrize(item)" :style="furnishStyles.getPrizeBtn.value">已领取</div>
                  <div v-else class="equity_btn noJurisdiction" v-click-track="'ljlq-qy'" @click="getPrize(item)" :style="furnishStyles.getPrizeBtn.value">点击领取</div>
                </div>
                <img v-if="item.status === 3" src="//img10.360buyimg.com/imgzone/jfs/t1/253763/29/25701/5447/67bd5c6fF5d476128/0b36925731a17021.png" alt="" class="no-stock" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 品牌旗舰专区 -->
    <div class="branZone">
      <img :src="furnish.branZone" class="branZone-img" />
      <!-- 动态生成的热区按钮 -->
      <div class="hotBtn" v-for="(item, index) in showData" :key="index" v-click-track="`btn${index + 1}`" :style="item.style" @click="toLink(item)" />
    </div>
    <div ref="skuTitle" class="sku" v-if="skuList.length">
      <img :src="furnish.showSkuBg" class="sku-list-img" alt="" />
      <div class="sku-list">
        <div class="sku-item" v-for="(item, index) in skuList" :key="index" @click="gotoSkuPage(item.skuId)">
          <img v-if="item.showSkuImage" :src="item?.showSkuImage" alt="" />
          <div class="sku-price" :style="furnishStyles.priceColor.value">
            {{ item.jdPrice }}
          </div>
        </div>
      </div>
    </div>
    <div v-if="furnish.footerIsOpen === '1'" class="bottom-shop-share">
      <div class="to-shop" :style="furnishStyles.btnToTop.value" @click="goTop" />
    </div>
    <!-- 活动门槛 -->
    <Threshold :showPopup="showLimit" @closeDialog="showLimit = false" :canNotCloseJoin="furnish.canNotCloseJoinPopup" :data="baseInfo?.thresholdResponseList" />
    <!-- 非会员拦截 -->
    <OpenCard :showPopup="showOpenCard" @closeDialog="showOpenCard = false" :canNotCloseJoin="furnish.canNotCloseJoinPopup" />
    <!-- 规则 -->
    <VanPopup teleport="body" v-model:show="showRule">
      <RulePopup :rule="ruleTest" @close="showRule = false" />
    </VanPopup>
    <!--我的奖品-->
    <VanPopup teleport="body" v-model:show="showMyPrize">
      <MyPrize v-if="showMyPrize" @close="showMyPrize = false" @showCardNum="showCardNum" @savePhone="showSavePhone" />
    </VanPopup>
    <!-- 保存地址弹窗 -->
    <VanPopup teleport="body" v-model:show="showSaveAddress">
      <SaveAddress :addressId="addressId" @close="closeAddress" />
    </VanPopup>
    <!-- 展示卡密 -->
    <VanPopup teleport="body" v-model:show="copyCardPopup">
      <CopyCard :detail="cardDetail" @close="copyCardPopup = false" />
    </VanPopup>
    <VanPopup teleport="body" v-model:show="isShowConfirmPopup">
      <GiftConfirm :giftInfo="giftItem" @close="isShowConfirmPopup = false" :multiplePrizeNum="multiplePrizeNum" :multiplePrizeCanReceiveNum="multiplePrizeCanReceiveNum" @upData="upDataInit" @drawSuccess="drawSuccessFun" />
    </VanPopup>
  </div>
</template>
<script setup lang="ts">
import { computed, inject, reactive, ref, watchEffect } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import { callShare } from '@/utils/platforms/share';
import constant from '@/utils/constant';
import { gotoSkuPage } from '@/utils/platforms/jump';
import RulePopup from '../components/RulePopup.vue';
import Threshold from '../components/Threshold.vue';
import GiftConfirm from '../components/GiftConfirm.vue';
import OpenCard from '../components/OpenCard.vue';
import SaveAddress from '../components/SaveAddress.vue';
import CopyCard from '../components/CopyCard.vue';
import dayjs from 'dayjs';
import MyPrize from '../components/MyPrize.vue';
import { defaultLadderPrizeList, defaultMultiplePrizeList, defaultStateList } from '../ts/default';
import { marginTop } from 'html2canvas/dist/types/css/property-descriptors/margin';

const decoData = inject('decoData') as DecoData;
const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
const endTime = ref(0);
const isStart = ref(false);
const isEnd = ref(false);
const startTime = ref(0);
// 热区数据
const showData = ref([]);
const getTime = () => {
  startTime.value = new Date(baseInfo.startTime).getTime();
  endTime.value = new Date(baseInfo.endTime).getTime();
  const now = new Date().getTime();
  if (now > startTime.value) {
    isStart.value = true;
  }
  if (now < startTime.value) {
    isStart.value = false;
  }
  if (now > endTime.value) {
    isEnd.value = true;
  }
  if (now < endTime.value) {
    isEnd.value = false;
  }
};
// 店铺名称
const shopName = ref(baseInfo.shopName);
// 门槛弹窗
const showOpenCard = ref(false);
// 规则弹窗
const showRule = ref(false);
// 活动规则
const ruleTest = ref('');
// 我的奖品弹窗
const showMyPrize = ref(false);
// 单次领取弹窗
const isShowConfirmPopup = ref(false);
// 曝光商品
const showGoods = ref(false);
type Prize = {
  prizeImg: string;
  prizeType: number;
  prizeName: string;
  stepAmount: number;
  remainCount: number;
  sendTotalCount: number;
  buyTimes: number;
};
// 订单限制 1件数 2 金额
const itemTotalOrAmount = ref(1);
const multiplePrizeList = ref<Prize>([]);
multiplePrizeList.value = defaultMultiplePrizeList;

// 进度条
const progressBar = ref(0);
const progressWidth = ref('');

const giftItem = ref({});
const toLink = (item: any) => {
  window.location.href = `${item.url}`;
};
// 活动商品列表
type Sku = {
  skuId: string;
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const skuList = ref<Sku[]>([]);
const skuTitle = ref();
const buyNow = () => {
  skuTitle.value.scrollIntoView({ behavior: 'smooth' });
};
// 展示门槛显示弹框
const showLimit = ref(false);

// 保存实物地址相关
const showSaveAddress = ref(false);
const addressId = ref('');
const userReceiveRecordId = ref('');

// 活动规则相关
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error: any) {
    console.error();
  }
};
const showMyPrizePop = () => {
  showMyPrize.value = true;
};

// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  id: 1,
  prizeName: '',
  prizeImg: '',
  cardDesc: '',
  cardNumber: '',
  cardPassword: '',
  exchangeImg: '',
});
// 展示卡密
const showCardNum = (distribute: any) => {
  // 把result的值赋值给cardDetail
  Object.keys(cardDetail).forEach((item) => {
    cardDetail[item] = distribute[item];
  });
  copyCardPopup.value = true;
};
// 领取京元宝权益
const savePhonePopup = ref(false);
const planDesc = ref('');

interface ActivityGiftRecord {
  avatar: string;
  nickName: string;
  prizeName: string;
}

const activityDataInfo = ref<any>({});
const activityGiftRecords = reactive([] as ActivityGiftRecord[]);
// 多选奖品可领取数量
const multiplePrizeCanReceiveNum = ref(0);
// 多选奖品数量
const multiplePrizeNum = ref(0);
// 活动要求购买件数
const activityItemTotal = ref(0);
// 订单笔数 具体有几笔
const orderStrokeStatus = ref(0);
// 订单笔数 1:单笔 2:多笔
const orderStrokeCount = ref(1);
// 订单金额
const orderRestrainAmount = ref(0);
// 订单商品  0 全店 1 指定 2 排除
const orderSkuisExposure = ref(0);
// 已购数量
const itemTotal = ref(0);
const oldOrderEndTime = ref();
const oldOrderStartTime = ref();
const hasOrdBefore = ref(false);
const text = computed(() => (hasOrdBefore.value ? '恭喜您获得特权资格，下单复购有机会领取视频月卡，数量有限，先到先得，快去选购吧！' : `您${dayjs(oldOrderStartTime.value).format('YYYY年MM月DD日')}~${dayjs(oldOrderEndTime.value).format('YYYY年MM月DD日')}未购，未获得特权领取资格，速速购买并参与${dayjs().month() + 2}月活动`));

// 主接口获取信息
const getActivityInfo = async () => {
  try {
    const { data } = await httpRequest.post('/96090/activity');
    activityDataInfo.value = data;
    hasOrdBefore.value = data.hasOrdBefore;
    if (!hasOrdBefore.value) {
      if (furnish.isShowJump && furnish.jumpUrl) {
        window.location.href = furnish.jumpUrl;
        return;
      }
      showLimit.value = true;
    }
    data.multiplePrize.forEach((item: any) => {
      item.remainCount = item.remainCount >= 0 ? item.remainCount : 0;
    });
    multiplePrizeList.value = data.multiplePrize;
    multiplePrizeCanReceiveNum.value = data.multiplePrizeCanReceiveNum;
    multiplePrizeNum.value = data.multiplePrizeNum;
    activityItemTotal.value = data.activityItemTotal;
    orderStrokeStatus.value = data.orderStrokeStatus;
    orderStrokeCount.value = data.orderStrokeCount;
    orderRestrainAmount.value = data.orderRestrainAmount;
    itemTotal.value = data.itemTotal;
    oldOrderEndTime.value = data.oldOrderEndTime;
    oldOrderStartTime.value = data.oldOrderStartTime;
    progressBar.value = data.progressBar;
    progressWidth.value = `${data.progressBar * 100}%`;
    orderSkuisExposure.value = data.orderSkuisExposure;
    itemTotalOrAmount.value = data.itemTotalOrAmount;
    // progressBar.value = 0.6;
    // progressWidth.value = '60%';
  } catch (error: any) {
    console.error(error);
  }
};
const pageNum = ref(1);
const total = ref(0);
const pagesAll = ref(0);
// 获取曝光商品
const getSkuList = async () => {
  try {
    const { data } = await httpRequest.post('/96090/getExposureSkuPage', {
      type: 0,
      pageNum: pageNum.value,
      pageSize: 20,
    });
    skuList.value = data.records;
    total.value = data.total;
    pagesAll.value = data.pages;
  } catch (error: any) {
    console.error(error);
  }
};
// 初始化
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    getTime();
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    showData.value = furnish.hotZoneList;
    showData.value.forEach((item: any) => {
      const style: any = {};
      style.width = `${(item.width * 2) / 100}rem`;
      style.height = `${(item.height * 2) / 100}rem`;
      style.position = 'absolute';
      style.top = `${(item.top * 2) / 100}rem`;
      style.left = `${(item.left * 2) / 100}rem`;
      item.style = style;
    });
    await Promise.all([getActivityInfo(), getSkuList()]);
    closeToast();
    if (baseInfo.thresholdResponseList[0]?.thresholdCode === 1 || baseInfo.thresholdResponseList[0]?.thresholdCode === 2) {
      showLimit.value = true;
    }
    if (baseInfo.thresholdResponseList[0]?.thresholdCode === 4) {
      showOpenCard.value = true;
      console.log('非会员');
      return;
    }
  } catch (error: any) {
    closeToast();
  }
};

const upDataInit = async () => {
  await Promise.all([getActivityInfo(), getSkuList()]);
};

// 校验领奖资格
const checkQualification = (val) => {
  if (!isStart.value) {
    showToast('活动未开始~');
    return false;
  }
  if (isEnd.value) {
    showToast('活动已结束~');
    return false;
  }
  if (val.status === 0) {
    showToast('您不符合活动条件，无法领取~');
    return false;
  }
  if (val.status === 2) {
    showToast('您已领取过该奖品~');
    return false;
  }
  if (val.status === 3) {
    showToast('手慢了，奖品已领光~');
    return false;
  }
  if (val.status === 4) {
    showToast('领取数量已经达到上限~');
    return false;
  }
  return val.status === 1;
};

// 领取各个奖品判断
const getPrize = async (prize: any) => {
  if (baseInfo.thresholdResponseList[0]?.thresholdCode === 1501) {
    showLimit.value = true;
    return;
  }
  // 校验资格
  if (!checkQualification(prize)) {
    return;
  }
  giftItem.value = prize;
  isShowConfirmPopup.value = true;
};

// 确认领取弹窗的回调
const drawSuccessFun = async () => {
  isShowConfirmPopup.value = false;
};

const showSavePhone = (id: string, desc: string) => {
  userReceiveRecordId.value = id;
  planDesc.value = desc;
  showMyPrize.value = false;
  savePhonePopup.value = true;
};

const closeAddress = () => {
  showSaveAddress.value = false;
  upDataInit();
};

watchEffect(() => {
  // 收集依赖
  if (baseInfo.startTime === dayjs().unix() * 1000) {
    window.location.reload();
  }
});
init();

const shareAct = () => {
  const shareConfig = JSON.parse(window.sessionStorage.getItem(constant.LZ_SHARE_CONFIG) ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};

const goTop = () => {
  document.documentElement.scrollTop = 0;
  document.body.scrollTop = 0;
};
</script>

<style lang="scss" scoped>
@font-face {
  font-family: 'FZZZHJTFont';
  src: url('../style/fzzzhjt.ttf') format('truetype');
}
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;
  height: 5rem;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.3rem 0 0 0.3rem;
    display: flex;
    justify-content: space-between;
  }

  .create-img {
    .header-btn {
      div {
        margin-top: -0.18rem;
      }
    }
  }

  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    width: 1.28rem;
    height: 0.45rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-repeat: no-repeat;
    background-size: 100%;
  }
}

.hotZoneBox {
  width: 7.5rem;
  margin: 0 auto;
  position: relative;
  top: 0.2rem;
  .hotZone {
    width: 7.5rem;
  }
  .prizeBox {
    position: absolute;
    top: 1rem;
    .qualificationBg {
      width: 7.5rem;
      height: 1.3rem;
      margin: 0 auto;
      background-repeat: no-repeat;
      background-size: 100%;
      padding: 0.1rem 0 0 0.48rem;
      font-weight: bolder;
      .title {
        font-size: 0.24rem;
        font-weight: 600;
        width: 4rem;
        margin: 0 0 0 1.5rem;
        text-align: center;
      }
      .text {
        font-size: 0.17rem;
        font-weight: 400;
        font-stretch: normal;
        letter-spacing: 0;
        width: 4rem;
        height: 0.89rem;
        margin: 0 0 0 1.5rem;
        text-align: center;
        // padding: 0.15rem;
      }
      .toBuyBtn {
        width: 0.95rem;
        height: 0.94rem;
        border-radius: 100%;
        margin: 0 auto;
        background-repeat: no-repeat;
        background-size: 100%;
        position: absolute;
        top: 0.4rem;
        right: 0.5rem;
        //background-color: #fff;
      }
    }
    .orderTipsText {
      width: 6.5rem;
      margin: 0 auto;
      font-size: 0.18rem;
      //div {
      //  line-height: 0.3rem;
      //}
    }
    .choosePrizes {
      width: 6.7rem;
      height: 3.5rem;
      background-repeat: no-repeat;
      background-size: 100%;
      margin: 0.4rem auto 0;
      .prizeLimit {
        text-align: center;
        height: 1rem;
        line-height: 1rem;
        margin-top: 1.3rem;
        color: #f0f8ff;
      }
      .choosePrizesBox {
        width: 6.9rem;
        height: auto;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(30%, 1fr));
        grid-gap: 0.1rem;
        box-sizing: border-box;
        overflow: hidden;
        margin: 1.5rem auto 0;
        .list-view {
          width: 100%;
          height: auto;
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(30%, 1fr));
          grid-gap: 0.1rem;
          box-sizing: border-box;
          overflow: hidden;
          .itemBg {
            width: 2.05rem;
            height: 2.5rem;
            border-radius: 0.25rem;
            background-repeat: no-repeat;
            background-size: 100% 100%;
            color: #fff;
            padding: 0.13rem;
            text-align: center;
            border: 1px solid hwb(0deg 100% 0/27%);
            position: relative;
            margin-bottom: 0.1rem;
            .equity_img {
              margin: 0.15rem auto;
              width: 1.2rem;
              height: 1.2rem;
            }
            .equity_name {
              font-size: 0.22rem;
              line-height: 0.22rem;
              // 单行超出展示...
              word-break: break-all;
              display: -webkit-box;
              overflow: hidden;
              text-overflow: ellipsis;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
            }
            .btm-info {
              display: flex;
              align-items: center;
              justify-content: space-between;
              /* background-color: #ab7f7f59; */
              height: 0.35rem;
              font-size: 0.25rem;
              margin: 0.4rem 0 0 0.09rem;
            }
            .equity_num {
            }
            .equity_btn {
              width: 0.8rem;
              height: 0.28rem;
              border-radius: 0.22rem;
              background-repeat: no-repeat;
              background-size: 100%;
              font-size: 0.16rem;
              line-height: 0.28rem;
            }
            .noJurisdiction {
              filter: grayscale(1);
            }
            .no-stock {
              position: absolute;
              top: 0.6rem;
              left: 0.5rem;
              width: 1.3rem;
            }
          }
          // .equity_btn::after {
          //   content: '';
          //   position: absolute;
          //   top: 0;
          //   left: 0;
          //   width: 100%;
          //   height: 100%;
          //   background-color: rgba(0, 0, 0, 0.5); /* 半透明灰色 */
          //   border-radius: 0.22rem; /* 与按钮的圆角保持一致 */
          //   pointer-events: none; /* 确保遮罩层不会阻止点击事件 */
          // }
        }
      }
    }
  }
}
.branZone {
  width: 7.5rem;
  // height: 4.24rem;
  margin: 0 auto;
  margin-top: 0.3rem;
  position: relative;
  .branZone-img {
    width: 7.5rem;
    // height: 4.24rem;
  }
}
.sku {
  width: 7.5rem;
  padding: 0.2rem 0;
  margin: 0rem auto;
  position: relative;
  .sku-list-img {
    width: 7.5rem;
    height: auto;
  }
  .sku-list {
    width: 6.7rem;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 1.35rem;
    // background: #ff6420;
    //opacity: 0.8;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
  }
  .sku-item {
    width: 3.26rem;
    overflow: hidden;
    position: relative;
    margin-bottom: 0.12rem;
    .sku-price {
      font-size: 0.4rem;
      //color: #7d4513;
      text-align: center;
      font-weight: 600;
      position: absolute;
      left: .53rem;
      bottom: 0;
    }
  }
}

.bottom-div {
  padding-top: 0.2rem;
  padding-bottom: 1rem;
  font-size: 0.24rem;
  color: #000;
  text-align: center;
}
.bottom-shop-share {
  margin: 0 auto;
  .to-shop {
    margin: 0 auto;
    height: 2.5rem;
    width: 7.5rem;
    background-repeat: no-repeat;
    background-size: 100%;
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
