/* eslint-disable */
import { MutationTree } from 'vuex';
import { RootState } from './state';
import { set } from 'lodash';

const mutations: MutationTree<RootState> = {
  setPetInfo(state: RootState, value: any) {
    state.petInfo = value;
  },
  setToothImg(state: RootState, value: string) {
    state.toothImg = value;
  },
  setCheckId(state: RootState, value: string) {
    state.checkId = value;
  },
  setSelectVariety(state: RootState, value: any) {
    state.createPetForm.petBreed = value;
  },
  setCreatePetForm(state: RootState, value: any) {
    state.createPetForm = value;
  },
  setQuestionRes(state: RootState, value: any) {
    state.qustionRes = value;
  },
  setAiRes(state: RootState, value: any) {
    state.aiRes = value;
  },
};
export default mutations;
