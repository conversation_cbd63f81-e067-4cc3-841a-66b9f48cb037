.main-view {
    width: 100%;
    height: 100vh;
    background-color: #fff;
    box-sizing: border-box;
    padding-top: 0.43rem;
    position: relative;
}
.common-btn {
    width: 6.23rem;
    height: 1.13rem;
    border-radius: 0.565rem;
    text-align: center;
    font-size: 0.48rem;
    line-height: 1.13rem;
    position: absolute;
    left: 0.63rem;
    bottom: 0.5rem;
}
.common-dog {
    width: 7.5rem;
    height: 4rem;
    position: absolute;
    left: 0;
    bottom: 0;
}
.common-text-popup {
  width: 6.75rem;
  height: auto;
  max-height: 10.4rem;
  border-radius: 0.24rem;
  background-color: #fff;
  color: #000;
  font-size: 0.28rem;
  line-height: 0.5rem;
  box-sizing: border-box;
  padding:  0.6rem 0.45rem;
  .title {
    width: 100%;
    text-align: center;
    font-size: 0.48rem;
    margin-bottom: 0.45rem;
  }
}
.home-bottom-btn {
    width: 6.24rem;
    height: 1.13rem;
    border-radius: 0.565rem;
    font-size: 0.48rem;
    line-height: 1.13rem;
    text-align: center;
    margin-left: 0.63rem;
    margin-bottom: 0.25rem;
}
.home-check-box {
  width: 7.5rem;
  margin-bottom: 0.4rem;
  display: flex;
  justify-content: center;
  font-size: 0.28rem;
  color: #333333;
}
.link-text {
  font-weight: bold;
  text-decoration-line: underline;
}
.home-content-view {
    width: 6.8rem;
    margin-left: 0.35rem;
    height: 4.6rem;
    box-sizing: border-box;
    position: relative;
    border-radius: 0.24rem;
    overflow: hidden;
    padding-top: 0.6rem;
    padding-left: 0.35rem;
    padding-right: 0.35rem;
    .title-text {
        width: 100%;
        margin-bottom: 0.55rem;
        font-size: 0.48rem;
        text-align: center;
    }
    .text-item {
        width: 100%;
        margin-bottom: 0.44rem;
        font-size: 0.25rem;
        line-height: 0.35rem;
        font-weight: 100;
        text-align: center;
    }
    .open-popup {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.36rem;
        line-height: 0.4rem;
        height: 0.4rem;
        .right-icon {
            width: 0.32rem;
            height: 0.32rem;
            margin-left: 0.1rem;
        }
    }
    .bottom-text-box {
        width: 100%;
        height: 1.1rem;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 0.26rem;
        position: absolute;
        bottom: 0;
        left: 0;
    }
}
.home-right-icon {
  width: 0.95rem;
  position: absolute;
  right: 0.34rem;
}
.home-dog {
  width: 7.5rem;
  height: 5.76rem;
}
.close-icon {
    width: 0.56rem;
    height: 0.56rem;
}
.fill-gary {
    filter: grayscale(1);
    opacity: 0.5;
}
.pet-close-icon {
    margin-left: 6.24rem;
    margin-bottom: 0.3rem;
}
.pet-popup {
    width: 6.8rem;
    height: 11.6rem;
    position: relative;
}
.pet-content {
    width: 6.8rem;
    height: 10.76rem;
    background-color: #fff;
    padding-top: 0.5rem;
    border-radius: 0.24rem;
    overflow-y: scroll;
    box-sizing: border-box;
    .pet-title {
        width: 100%;
        margin-bottom: 0.4rem;
        text-align: center;
        font-size: 0.48rem;
        color: #000;
    }
    .pet-item {
        border-radius: 0.3rem;
        width: 6.35rem;
        margin-left: 0.225rem;
        margin-bottom: 0.25rem;
        height: 1.74rem;
        z-index: 40;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-left: 0.3rem;
        padding-right: 0.35rem;
        border-width: 0.02rem;
        border-style: solid;
        border-color: #fff;
    }
    .pet-img {
        width: 0.94rem;
        height: 0.94rem;
        box-sizing: border-box;
        background-color: #fff;
        border-radius: 50%;
        padding: 0.02rem;
        overflow: hidden;
        img {
            width: 100%;
            height: 100%;
        }
    }
    .pet-icon {
        width: 0.98rem;
        height: 1.03rem;
    }
    .pet-info-wrapper {
        display: flex;
        flex-direction: column;
        margin-left: 0.26rem;
        width: 3.26rem;
    }
    .pet-info-wrapper .row-1-wrap {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        font-size: 0.26rem;
    }
    .pet-info-wrapper .row-1-wrap img {
        width: 0.23rem;
        height: 0.23rem;
        margin-left: 0.15rem;
    }
    .pet-info-wrapper .row-2-wrap {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        font-size: 0.21rem;
        margin-top: 0.05rem;
    }
    .pet-info-wrapper .row-2-wrap text {
        overflow: hidden; /*溢出隐藏*/
        text-overflow: ellipsis; /*溢出用省略号显示*/
        white-space: nowrap;
        width: 1.8rem;
        line-height: 0.24rem;
    }
    .pet-info-wrapper .row-2-wrap > view {
        min-width: 1.17rem;
        height: 0.27rem;
        border-radius: 1rem;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 0.2rem;
        border: 0.02rem #000 solid;
        margin-left: 0.1rem;
    }
    .pet-info-wrapper .row-3-wrap {
        font-size: 0.24rem;
        margin-top: 0.05rem;
    }
}
.pet-add-btn {
    width: 6.34rem;
    height: 0.9rem;
    margin-left: 0.225rem;
}
.pet-confirm-btn {
    width: 4.56rem;
    height: 1.13rem;
    margin-top: 0.85rem;
    margin-left: 1.12rem;
    border-radius: 0.565rem;
    text-align: center;
    font-size: 0.48rem;
    line-height: 1.13rem;
}

#createFile {
  width: 7.5rem;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  overflow-y: scroll;
  background-color: #ebebeb;
  color: #333333;
  padding-top: 0.2rem;
  box-sizing: border-box;
}
.file_box {
  width: 7.5rem;
  height: 13.34rem;
  background-color: #fff;
  border-radius: 0.4rem;
  border-bottom-right-radius: unset;
  border-bottom-left-radius: unset;
  box-sizing: border-box;
  padding-top: 0.2rem;
  margin: 0.99rem auto 0;
  position: relative;
}

.file_box .choose_pet_avater {
  width: 1.58rem;
  height: 1.58rem;
  border-radius: 50%;
  border: 0.01rem solid #d61623;
  margin: -0.99rem auto 0;
  display: block;
}

.file_box .type {
  width: 5.62rem;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-content: center;
  margin: 0.4rem auto;
}

.file_box .type .nameBox {
  width: 2.69rem;
  height: 0.96rem;
  border-radius: 0.1rem;
  text-align: center;
  font-size: 0.24rem;
  line-height: 0.96rem;
}
.name1 {
  background-color: #f9eeef;
  color: #eb031d;
}
.name2 {
  background-color: #f9f9f9;
  color: #808285;
}

.file_box .input_box {
  width: 6.55rem;
  height: auto;
  margin: 0 auto;
}
.input_box .input_row {
  width: 6.55rem;
  height: 0.9rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
  margin-bottom: 0.4rem;
}
.input_row .form-row-label {
  width: 1.5rem;
  text-align: right;
  font-size: 0.24rem;
  color: #333333;
}
.form-row-content {
  width: 4.83rem;
  height: 0.9rem;
  border-radius: 0.1rem;
  background-color: #f9f9f9;
  color: #808285;
  font-size: 0.24rem;
  border: 0.01rem solid #ebebeb;
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: space-between;
  padding: 0.1rem 0.2rem 0.1rem 0.1rem;
  box-sizing: border-box;
}
.form-row-content input {
  width: 4.46rem;
  height: 0.64rem;
  padding: 0;
  border: none;
  background: none;
  background-color: #f9f9f9;
  box-sizing: border-box;
  color: #808285;
  font-size: 0.24rem;
}
.form-row-content .placeholder {
  color: #808285;
  font-size: 0.24rem;
}
.form-row-content image {
  width: 0.12rem;
  height: 0.22rem;
}

.choose_public {
  width: 4.83rem;
  height: 0.9rem;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-content: center;
  margin: 0.4rem 0 0.4rem 0.2rem;
}

.public_name {
  width: 2.26rem;
  height: 0.9rem;
  line-height: 0.9rem;
  border-radius: 0.1rem;
  text-align: center;
  font-size: 0.24rem;
}

.input_box .text1 {
  color: #808285;
  font-size: 0.2rem;
  text-align: center;
  margin-top: -0.2rem;
}
.file_box .btn {
  width: 3.05rem;
  height: 0.95rem;
  margin: 0.55rem auto;
}
.file_box .btn image {
  width: 100%;
  height: 100%;
}
.right-icon {
  width: 0.16rem;
}

.photo-desc {
  width: 7.5rem;
  height: 9.72rem;
}
.photo-bottom {
  width: 6.9rem;
  display: flex;
  justify-content: space-between;
  position: absolute;
  left: 0.3rem;
  bottom: 0.5rem;
  .item-btn {
      width: 3.3rem;
      height: 1rem;
      font-size: 0.48rem;
      line-height: 1rem;
      text-align: center;
      border-radius: 0.5rem;
  }
}
.photo-review {
  width: 6.8rem;
  height: 4.46rem;
  margin-bottom: 0.3rem;
  border-radius: 0.24rem;
  margin-left: 0.35rem;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
.retake-photo {
  width: 7.12rem;
  margin-left: 0.19rem;
}
.upload-success-title {
  width: 6.8rem;
  height: 1.8rem;
  margin-left: 0.35rem;
  margin-bottom: 0.8rem;
  border-radius: 0.24rem;
  text-align: center;
  line-height: 1.8rem;
  font-size: 0.4rem;
  font-weight: bold;
}
.upload-info {
  width: 5.7rem;
  margin-left: 0.9rem;
  font-size: 0.28rem;
  line-height: 0.36rem;
  margin-bottom: 0.55rem;
  color: #323232;
}
.upload-error-title {
  width: 7.5rem;
  margin-bottom: 0.4rem;
  text-align: center;
  font-size: 0.48rem;
}
.upload-error-img {
  width: 6.46rem;
  margin-left: 0.52rem;
  margin-bottom: 0.45rem;
}
.upload-error-desc {
  width: 6.45rem;
  margin-left: 0.525rem;
}
.loading-box {
  width: 7.5rem;
  margin-top: 0.7rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.loading-info-box {
  width: 6.54rem;
  height: 6.82rem;
  border-radius: 0.24rem;
  margin-top: 0.9rem;
  box-sizing: border-box;
  padding-top: 0.3rem;
  padding-left: 0.23rem;
  padding-right: 0.23rem;
  .loading-info-title {
    width: 100%;
    font-size: 0.48rem;
    text-align: center;
    margin-bottom: 0.3rem;
  }
  .loading-info-dog {
    width: 100%;
    margin-bottom: 0.4rem;
  }
  .loading-info-text {
    font-size: 0.28rem;
    line-height: 0.4rem;
  }
}
.qustion-slise-box {
  width: 2.62rem;
  height: 0.25rem;
  border-radius: 0.125rem;
  margin-left: 2.44rem;
}
.qustion-slise-item {
  height: 0.25rem;
  border-radius: 0.125rem;
}
.qustion-step-text {
  width: 7.5rem;
  text-align: center;
  font-size: 0.28rem;
  color: #333333;
  margin-top: 0.15rem;
  margin-bottom: 0.45rem;
}
.qustion-text {
  width: 7.5rem;
  height: 1.68rem;
  font-size: 0.4rem;
  font-weight: bold;
  line-height: 0.56rem;
  color: #333333;
  text-align: center;
}
.qustion-no-btn {
  width: 6.9rem;
  height: 1rem;
  margin-bottom: 0.55rem;
  margin-left: 0.3rem;
  border-radius: 0.5rem;
  text-align: center;
  background-color: #eaeaea;
  color: #323232;
  font-size: 0.36rem;
  line-height: 1rem;
  input {
    width: 100%;
    height: 100%;
    border: unset;
    background: unset;
    text-align: center;
    color: #323232;
    font-size: 0.36rem;
    line-height: 1rem;
  }
}
.qustion-yes-btn {
  width: 6.9rem;
  height: 1rem;
  margin-bottom: 0.55rem;
  margin-left: 0.3rem;
  border-radius: 0.5rem;
  text-align: center;
  background-color: #ec001a;
  color: #fff;
  font-size: 0.36rem;
  line-height: 1rem;
}
.qustion-step-tips {
  width: 6.9rem;
  height: 2rem;
  margin-left: 0.3rem;
  margin-top: 0.75rem;
  border-radius: 0.24rem;
  background-color: #f6f6f6;
  padding-left: 0.35rem;
  padding-right: 0.35rem;
  font-size: 0.28rem;
  font-weight: 200;
  line-height: 0.36rem;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}
.result-content-box {
  width: 7.5rem;
  height: 100vh;
  overflow-y: scroll;
  box-sizing: border-box;
}
.qustion-bottom {
  width: 6.24rem;
  margin-left: 0.33rem;
  margin-top: 0.5rem;
  display: flex;
  justify-content: space-around;
  .item-btn {
      width: 3rem;
      height: 1rem;
      font-size: 0.48rem;
      line-height: 1rem;
      text-align: center;
      border-radius: 0.5rem;
  }
}
.logo-img {
  width: 1.75rem;
  margin-left: 2.875rem;
  margin-top: 0.22rem;
  margin-bottom: 0.22rem;
}
.result-content {
  width: 7rem;
  height: 11.9rem;
  margin-left: 0.25rem;
  margin-bottom: 0.25rem;
  background-color: #fff;
  border-radius: 0.24rem;
  box-shadow: 0px 0px 0.18rem 0px rgba(76, 76, 76, 0.3);
  box-sizing: border-box;
  padding-top: 0.6rem;
  .title {
    width: 100%;
    margin-bottom: 0.65rem;
    text-align: center;
    font-size: 0.48rem;
    color: #000;
    line-height: 0.5rem;
  }
  .tips {
    width: 100%;
    text-align: center;
    font-size: 0.28rem;
    line-height: 0.5rem;
    color: #808285;
  }
}
.ai-res-box {
  width: 6.2rem;
  margin-left: 0.4rem;
  height: 7.65rem;
  background-size: 100%;
  background-repeat: no-repeat;
  background-position: top;
  box-sizing: border-box;
  padding-top: 2.45rem;
  position: relative;
  .ai-item-box {
    width: 5.6rem;
    height: 1.4rem;
    background-color: #fff;
    margin-bottom: 0.3rem;
    margin-left: 0.3rem;
    border-radius: 0.24rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.28rem;
    line-height: 0.4rem;
    box-sizing: border-box;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
  .num-text {
    font-size: 0.72rem;
    font-weight: bold;
  }
}
.qustion-res-item {
  width: 100%;
  margin-bottom: 0.3rem;
  padding-left: 0.6rem;
  padding-right: 0.4rem;
  display: flex;
  justify-content: space-between;
  font-size: 0.28rem;
}
.qustion-check-box {
  width: 0.46rem;
  height: 0.46rem;
  border-radius: 0.07rem;
  background-color: #e9e9e9;
}
.qustion-desc {
  width: 4.5rem;
  line-height: 0.4rem;
  color: #333333;
}
.qustion-tips-box {
  width: 5.86rem;
  height: 1.96rem;
  border-radius: 0.24rem;
  background-color: #ffefed;
  box-sizing: border-box;
  padding-left: 0.2rem;
  padding-right: 0.2rem;
  font-size: 0.28rem;
  line-height: 0.4rem;
  color: #ec001a;
  display: flex;
  align-items: center;
  margin: auto;
}
.page-no-box {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: absolute;
  bottom: 0.5rem;
  font-size: 0.28rem;
}
.bottom-div {
  width: 2.7rem;
  margin-top: 0.3rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.direction-icon {
  width: 0.39rem;
}
.page-point {
  width: 0.19rem;
  height: 0.19rem;
  border-radius: 50%;
  margin: 0.08rem;
}
// 海报页
#poster {
  width: 7.5rem;
  height: auto;
  position: absolute;
  left: 15rem;
  top: -150rem;
}
.post-ai-res-bg {
  width: 6.74rem;
  margin: auto;
  height: 11.86rem;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: top;
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
  .ai-item-box {
    width: 5.6rem;
    height: 1.4rem;
    background-color: #fff;
    margin-bottom: 0.3rem;
    margin-left: 0.57rem;
    border-radius: 0.24rem;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    // align-items: center;
    box-sizing: border-box;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
  .desc {
    font-size: 0.28rem;
    height: 0.9rem;
    margin-top: 0.15rem;
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  .num-text {
    font-size: 0.72rem;
    height: 0.9rem;
    font-weight: bold;
  }
}
.ai-res-time {
  width: 100%;
  font-size: 0.28rem;
  color: #808285;
  text-align: center;
  margin-top: 0.88rem;
  margin-bottom: 0.4rem;
}
.ai-res-desc {
  width: 100%;
  font-size: 0.28rem;
  line-height: 0.35rem;
  color: #333333;
  text-align: center;
}
.ai-tooth-img {
  width: 5.7rem;
  margin: auto;
  height: 2.76rem;
  margin-top: 0.4rem;
  margin-bottom: 0.2rem;
  border-radius: 0.24rem;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}
.post-question-bg {
  width: 7.06rem;
  margin: auto;
  height: 9.11rem;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: top;
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
  padding-top: 2.6rem;
}
.post-bottom-box {
  width: 6.74rem;
  margin: auto;
  height: 2.1rem;
  display: flex;
  justify-content: space-between;
  font-size: 0.28rem;
  color: #808285;
}
.qrcode-box {
  width: 2.08rem;
  height: 2.08rem;
  border-radius: 0.16rem;
  border-style: dashed;
  border-color: #ec001a;
  border-width: 0.02rem;
  overflow: hidden;
}
.qr-code {
  width: 2.08rem !important;
  height: 2.08rem !important;
}
.bottom-tip {
  width: 7.5rem;
  text-align: center;
  position: absolute;
  left: 0;
  top: 24.2rem;
  font-size: 0.28rem;
}
.end-page {
  width: 100%;
  height: auto;
  background-color: #fff;
  position: relative;
}