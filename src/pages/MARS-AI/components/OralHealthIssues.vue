<template>
    <VanPopup teleport="body" v-model:show="show" position="center" :close-on-click-overlay="true">
        <div>
            <img class="pet-close-icon close-icon" :src="require('../asset/closeIcon.png')" @click="closePopup()"/>
            <div class="common-text-popup">
                <div class="title">口腔健康问题</div>
                <div style="margin-bottom: 0.6rem;">
                    &nbsp;&nbsp;汪汪爱牙宝利用人工智能分析牙结石堆积的迹象以及牙龈发炎的迹象。
                </div>
                <div style="margin-bottom: 0.6rem;">
                    &nbsp;&nbsp;牙菌斑是唾液、食物残渣和细菌在牙齿表面堆积形成的。随着时间的推移，牙菌斑会不断堆积并硬化成一种粗糙的褐色物质，称为牙结石。
                </div>
                <div>
                    &nbsp;&nbsp;狗狗牙齿上的牙菌斑和牙结石堆积会刺激牙龈，导致牙龈发红、肿胀。随着时间的推移，病情可能会发展并恶化，对牙龈和牙齿支撑结构造成不可逆的损伤，这可能是痛苦的，并可能导致牙齿脱落。
                </div>
            </div>
        </div>
    </VanPopup>
</template>
<script lang="ts" setup>
import { FLAGS } from 'html2canvas/dist/types/dom/element-container';
import { emit } from 'process';
import { defineProps, computed, ref } from 'vue';

const props = defineProps({
  isShow: {
    type: Boolean,
    default: false,
  },
});

const show = computed(() => props.isShow);
const emits = defineEmits(['closePopup']);
const closePopup = () => {
  emits('closePopup');
};

</script>
<style lang="scss" scoped>
@import '../config/page.scss';
</style>
