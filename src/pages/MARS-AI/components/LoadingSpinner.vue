<template>
  <div class="rectangle-loader" :style="{ '--rectangle-color': spinnerColor, '--rectangle-size': spinnerSize + 'rem' }">
    <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
      <g transform="translate(50, 50)">
        <g v-for="(width, index) in widths" :key="index" :transform="`rotate(${angle * index}) translate(30)`">
          <rect :x="5 - width / 2" :y="-2.5 - (5 - heights[index] / 2)" :width="width" :height="heights[index]" fill="var(--rectangle-color)" rx="2" ry="2" />
        </g>
      </g>
    </svg>
    <div class="tips-box">正在提交照片进行审核...</div>
  </div>
</template>

<script lang="ts" setup>
import { defineProps, computed } from 'vue';

const props = defineProps({
  spinnerColor: {
    type: String,
    default: '#3498db', // 默认颜色
  },
  spinnerSize: {
    type: Number,
    default: 100, // 默认大小
  },
});

const angle = computed(() => 360 / 12);

const widths = computed(() => {
  const minWidth = 0;
  const maxWidth = 17;
  const step = (maxWidth - minWidth) / 10;
  return Array.from({ length: 12 }, (_, i) => minWidth + i * step);
});

const heights = computed(() => {
  const minHeight = 0;
  const maxHeight = 8;
  const step = (maxHeight - minHeight) / 10;
  return Array.from({ length: 12 }, (_, i) => minHeight + i * step);
});
</script>

<style scoped>
.rectangle-loader {
  width: var(--rectangle-size);
  height: var(--rectangle-size) + 0.6rem;
  position: relative;
}

.rectangle-loader svg {
  transform-origin: center;
  animation: rotate 2s cubic-bezier(0.1, 0.25, 0.75, 0.9) infinite;
}

.tips-box {
  word-break: keep-all;
  text-align: center;
  display: flex;
  justify-content: center;
  font-size: 0.28rem;
  color: #333333;
  margin-top: 0.3rem;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
