<template>
    <div class="main-view" style="overflow-y: scroll;">
        <img v-if="imgurl" style="width: 7.5rem;height: auto;margin-bottom: 1rem;" :src="imgurl">
        <div class="bottom-tip" :style="{color: PAGE_CONFIG.mainBgColor}">长按保存结果</div>
        <div id="poster">
            <img class="logo-img" :src="require('../asset/logo.png')" alt="">
            <div class="post-ai-res-bg" :style="{ backgroundImage: `url(${require('../asset/'+ baseInfo.shopId +'/posterAiResBg.png')})`,color: `${PAGE_CONFIG.mainBgColor}`}">
                <div class="ai-res-time">分析时间：{{ dayjs().format('YYYY-MM-DD') }}</div>
                <div class="ai-res-desc">我们使用人工智能分析了【{{PetInfo?.petNick || store.state.qustionRes.value.petNick}}】<br>的牙齿照片，结果显示:</div>
                <div class="ai-tooth-img" :style="{backgroundImage: toothImg}"></div>
                <div class="ai-item-box">
                    <div class="desc">已分析牙齿总数</div>
                    <div class="num-text">{{ aiRes?.teethScanned }}</div>
                </div>
                <div class="ai-item-box">
                    <div class="desc">有牙结石堆积<br>可见迹象的牙齿数</div>
                    <div class="num-text">{{ aiRes?.tartarTeethCount }}</div>
                </div>
                <div class="ai-item-box">
                    <div class="desc">有牙龈发炎<br>可见迹象的区域数量</div>
                    <div class="num-text">{{ aiRes?.gumInflammationCount }}</div>
                </div>
            </div>
            <div class="post-question-bg" :style="{ backgroundImage: `url(${require('../asset/'+ baseInfo.shopId +'/posterQustionResBg.png')})`,color: `${PAGE_CONFIG.mainBgColor}` }">
                <div class="qustion-res-item">
                    <div style="display: flex;align-items: flex-start;">
                        <div class="qustion-check-box" style="margin-top: 0.15rem;">
                            <img v-if="qustionRes?.ifHalitosis" style="width: 100%;" :src="require('../asset/'+ baseInfo.shopId +'/check.png')"/>
                        </div>
                        <div style="height: 0.46rem; line-height: 0.46rem;margin-left: 0.05rem;" :style="{color: `${PAGE_CONFIG.mainBgColor}`}">口臭</div>
                    </div>
                    <div class="qustion-desc">口臭是由口腔中的细菌释放出难闻的化合物所导致，这可能是口腔有健康问题的一个迹象。</div>
                </div>
                <div class="qustion-res-item">
                    <div style="display: flex;align-items: flex-start;">
                        <div class="qustion-check-box" style="margin-top: 0.15rem;">
                            <img v-if="qustionRes?.ifBleeding" style="width: 100%;" :src="require('../asset/'+ baseInfo.shopId +'/check.png')"/>
                        </div>
                        <div style="height: 0.46rem; line-height: 0.46rem;margin-left: 0.05rem;" :style="{color: `${PAGE_CONFIG.mainBgColor}`}">出血</div>
                    </div>
                    <div class="qustion-desc">咀嚼或进食时出血可能提示有牙龈发炎。</div>
                </div>
                <div class="qustion-res-item">
                    <div style="display: flex;align-items: flex-start;">
                        <div class="qustion-check-box" style="margin-top: 0.15rem;">
                            <img v-if="qustionRes?.ifDiscomfort" style="width: 100%;" :src="require('../asset/'+ baseInfo.shopId +'/check.png')"/>
                        </div>
                        <div style="height: 0.46rem; line-height: 0.46rem;margin-left: 0.05rem;" :style="{color: `${PAGE_CONFIG.mainBgColor}`}">不适</div>
                    </div>
                    <div class="qustion-desc">口腔不适可能提示有口腔健康问题。</div>
                </div>
                <div class="qustion-tips-box" style="align-items: unset;">
                    <div v-if="!qustionRes?.ifHalitosis && !qustionRes?.ifBleeding && !qustionRes?.ifDiscomfort">如果您注意到上述列出的任何问题，应该带狗狗进行全面的牙科检查。如果这些问题不存在，也需要关注狗狗口腔的日常护理，定期为它刷牙。</div>
                    <div v-else>平日注意狗狗牙齿养护是重中之重,应当注意定期刷牙，同时配合牙齿清洁用品进行清洗。</div>
                </div>
            </div>
            <div class="post-bottom-box">
                <div style="margin-top: 0.5rem;">
                    识别右侧二维码<br>了解狗狗口腔健康问题潜在迹象
                </div>
                <div class="qrcode-box">
                    <vue-qrcode class="qr-code" :value="qrCodeActUrl" :options="{
                        color: {
                            dark: '#000',  // dots
                            light: '#ffffff' // background
                        }
                        }"></vue-qrcode>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { inject, shallowRef, provide, computed, ref, onMounted } from 'vue';
import { BaseInfo } from '@/types/BaseInfo';
import { useStore } from 'vuex';
import { RootState } from '../store/state';
import VueQrcode from '@chenfengyuan/vue-qrcode';
import html2canvas from 'html2canvas';
import dayjs from 'dayjs';
import { getPetBreed, getPetList, getCheckUpResult, getQaInfo, uploadToothImg, saveQaInfo, savePetInfo } from '../config/api';

const store = useStore<RootState>();

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
console.log('=>(App.vue:23) baseInfo', baseInfo);
const pathParams: any = inject('pathParams');
console.log('=>(App.vue:25) pathParams', pathParams);
const baseUserInfo: any = inject('baseUserInfo');
console.log('=>(App.vue:27) baseUserInfo', baseUserInfo);
const PAGE_CONFIG:any = inject('PAGE_CONFIG');
const PetInfo = computed(() => store.state.petInfo);
const toothImg = computed(() => store.state.toothImg);
const checkId = computed(() => store.state.checkId);

const emits = defineEmits(['toggleComponent']);

const qustionRes = computed(() => store.state.qustionRes);
const aiRes = computed(() => store.state.aiRes);
const qrCodeActUrl = window.location.href;
const imgurl = ref('');
const takephotos = async () => {
  const save2 = document.getElementById('poster') as HTMLAnchorElement;
  console.log(save2.offsetWidth, save2.offsetHeight);
  await html2canvas(save2, {
    backgroundColor: null, // 设置图片背景为透明
    scale: 2,
    width: save2.offsetWidth,
    height: save2.offsetWidth / 0.31,
    allowTaint: true,
    useCORS: true,
  }).then((canvas:any) => {
    const context: any = canvas.getContext('2d');
    context.mozImageSmoothingEnabled = false;
    context.webkitImageSmoothingEnabled = false;
    context.msImageSmoothingEnabled = false;
    context.imageSmoothingEnabled = false;
    const src64: any = canvas.toDataURL();
    const newImg: any = document.createElement('img');
    newImg.crossOrigin = 'Anonymous';
    newImg.src = src64;
    imgurl.value = newImg.src;
    // console.log(newImg.src);
  });
};
const init = () => {
  console.log(store.state.qustionRes.value);
  takephotos();
};

// const PAGE_CONFIG = `PAGE_CONFIG_`

onMounted(() => {
  init();
});
</script>
<style lang="scss" scoped>
@import '../config/page.scss';
</style>
