<template>
  <div id="createFile">
    <div class="file_box">
      <img class="choose_pet_avater" :src="form.petAvatar" @click="handlePhotoSelect" />
      <div class="type">
        <div class="public_name" :class="{ name1: form.petType == 2, name2: form.petType == 1 }">猫咪</div>
        <div class="public_name" :class="{ name1: form.petType == 1, name2: form.petType == 2 }">狗狗</div>
      </div>
      <div class="input_box">
        <div class="input_row">
          <div class="form-row-label">
            宠物昵称
          </div>
          <div class="form-row-content">
            <input v-model="form.petNick" data-name="petName" @input="onInputName" maxlength="6" placeholder-class="placeholder"
              placeholder="输入您的宠物信息" />
          </div>
        </div>
        <div class="input_row">
          <div class="form-row-label">
            <text style="color:#ff3333">*</text>宠物品种
          </div>
          <div class="form-row-content" @click="getPetConfig">
            <text>
              {{ form.petBreed }}
            </text>
            <img class="right-icon" src="https://img.alicdn.com/imgextra/i3/155168396/O1CN01Gz7gZD2BtQFF2wTnE_!!155168396.png" />
          </div>
        </div>
        <div class="input_row" @click="chooseDate">
          <div class="form-row-label">
            <text style="color:#ff3333">*</text>出生日期
          </div>
          <div class="form-row-content">
            <tex>{{ form.petBirth }}</tex>
            <img class="right-icon" src="https://img.alicdn.com/imgextra/i3/155168396/O1CN01Gz7gZD2BtQFF2wTnE_!!155168396.png" />
          </div>
        </div>
        <div class="input_row">
          <div class="form-row-label">
            宠物性别
          </div>
          <div class="choose_public">
            <div class="public_name" :class="{ name1: form.petGender == 1, name2: form.petGender != 1 }" @click="form.petGender = 1">公</div>
            <div class="public_name" :class="{ name1: form.petGender == 2, name2: form.petGender != 2 }" @click="form.petGender = 2">母</div>
          </div>
        </div>
        <div class="input_row">
          <div class="form-row-label">
            <text style="color:#ff3333">*</text>是否绝育
          </div>
          <div class="choose_public">
            <div class="public_name" :class="{ name1: form.petNeutered == 1, name2: form.petNeutered == 0 }" @click="form.petNeutered = 1">是</div>
            <div class="public_name" :class="{ name1: form.petNeutered == 0, name2: form.petNeutered == 1 }" @click="form.petNeutered = 0">否</div>
          </div>
        </div>
        <div class="input_row">
          <div class="form-row-label">是否默认展示</div>
          <div class="choose_public">
            <div class="public_name" :class="{ name1: form.petDefault, name2: !form.petDefault }" @click="form.petDefault = true">是</div>
            <div class="public_name" :class="{ name1: !form.petDefault, name2: form.petDefault }" @click="form.petDefault = false">否</div>
          </div>
        </div>
        <div class="text1">*设置为“是”会优先展示该宠物</div>
      </div>
      <div class="photo-bottom">
        <div class="item-btn" :style="`background-color: ${PAGE_CONFIG.mainBtnBgColor};color: ${PAGE_CONFIG.mainBtnTextColor};width: 3.2rem;`" @click="emits('toggleComponent', 'Home')">返回</div>
        <div class="item-btn" :style="`background-color: ${PAGE_CONFIG.mainBtnBgColor};color: ${PAGE_CONFIG.mainBtnTextColor};width: 3.2rem;`" @click="handleNextStep">提交</div>
      </div>
    </div>
    <VanPopup teleport="body" v-model:show="dateSelect" position="bottom">
      <VanDatePicker v-model="petBirthdayArr" @confirm="confirmDate" @cancel="dateSelect = false" :minDate="dayjs('1990-01-01').toDate()" :maxDate="dayjs().toDate()"></VanDatePicker>
    </VanPopup>
    <cropper v-if="isShowCropperPopup" :is-show="isShowCropperPopup" :image-src="needHandleImageSrc" @close-popup="isShowCropperPopup = false" @success-cropper="handleSuccessCropper"></cropper>
  </div>
</template>
<script lang="ts" setup>
import { inject, shallowRef, provide, computed, ref, onMounted, reactive } from 'vue';
import { BaseInfo } from '@/types/BaseInfo';
import { Field, Popup, DatePicker, IndexBar, IndexAnchor } from 'vant';
import dayjs from 'dayjs';
import { savePetInfo, getPetBreed } from '@/pages/MARS-AI/config/api';
import { ruleFormat, formatDate, getMobileModel, iosFace, androidFace } from '../config/common';
import Cropper from '../components/Cropper.vue';
import { any } from 'video.js/dist/types/utils/events';
import { useStore } from 'vuex';
import { RootState } from '../store/state';

const store = useStore<RootState>();

const format = ref('yyyy-MM-dd');

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
console.log('=>(App.vue:23) baseInfo', baseInfo);
const pathParams: any = inject('pathParams');
console.log('=>(App.vue:25) pathParams', pathParams);
const baseUserInfo: any = inject('baseUserInfo');
console.log('=>(App.vue:27) baseUserInfo', baseUserInfo);
const PAGE_CONFIG:any = inject('PAGE_CONFIG');

const isShowCropperPopup = ref(false);
// 定义表单对象
const form = reactive({
  petAvatar: 'https://img.alicdn.com/imgextra/i2/155168396/O1CN01FWskFX2BtQJY3wO2Z_!!155168396.jpg',
  petBirth: '', // 宠物生日
  petBreed: '', // 宠物品种
  petBreedId: 1, // 宠物品种id
  petDefault: true, // 是否默认展示（false：否；true：是）
  petGender: 0, // 宠物性别（0：未知；1：公；2：母）
  petImmunity: true, // 是否免疫（false：否；true：是）
  petNeutered: 0, // 是否绝育（0：否；1：是；2：未知）
  petNick: '',
  petPrescription: true, // 是否使用过处方粮（false：否；true：是）
  petType: 1, // 2 表示猫，1 表示狗
  petWeight: 0, // 宠物体重
});

const needHandleImageSrc = ref('');
const imgformatDate = ref();

const emits = defineEmits(['toggleComponent']);
// 处理头像校验
const handleAvatarValidate = (file: File) => {
  const reader = new FileReader();
  reader.onload = (e) => {
    console.log(1);
    const dataUrl = e.target?.result;
    const image = new Image();
    image.onload = () => {
      console.log(2);
      needHandleImageSrc.value = image.src;
      // 开始裁剪
      isShowCropperPopup.value = true;
      console.log(123);
    };
    if (typeof dataUrl === 'string') {
      image.src = dataUrl;
    }
  };
  reader.readAsDataURL(file);
};
const handlePhotoSelect = (): void => {
  const app = document.getElementById('container') as HTMLElement;
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = 'image/*';
  const system = getMobileModel();
  if (system === 'iPhone') {
    input.accept = 'image/jpeg,image/png,image/jpg';
  } else {
    input.accept = 'image/*';
  }
  input.style.display = 'none';
  app.appendChild(input);
  input.onchange = (event: Event) => {
    const target = event.target as HTMLInputElement;
    if (target && target.files && target.files.length > 0) {
      const file = target.files[0];
      // 进一步处理 file
      if (file) {
        console.log(file.size, '裁剪前');
        handleAvatarValidate(file);
      }
    }
  };
  input.click();
};
/**
 * 获取裁剪后的图片链接
 */
const handleSuccessCropper = (imageSrc: string) => {
  form.petAvatar = imageSrc;
};

const petBirthdayArr = ref([]);
const showCropImage = ref(false);
const res = ref({});

const dateSelect = ref(false);
const confirmDate = (date: any) => {
  form.petBirth = `${date.selectedValues[0]}-${date.selectedValues[1]}-${date.selectedValues[2]}`;
  dateSelect.value = false;
};

const onInputName = (event: any) => {
  form.petNick = event.target.value;
};

const isShowBredd = ref(false);
const getPetConfig = () => {
  console.log(form);
  // 暂存填写的宠物信息，方便回显
  store.commit('setCreatePetForm', form);
  // 切到宠物品种选择页面
  emits('toggleComponent', 'SelectVariety');
};

const chooseDate = () => {
  // 实现选择日期的逻辑
  dateSelect.value = true;
};

const handleNextStep = () => {
  // 实现下一步的逻辑
  // console.log(form);
  savePetInfo(form).then((res: any) => {
    if (res) {
      emits('toggleComponent', 'Home');
    }
  });
};
const init = () => {
  const { createPetForm } = store.state;
  console.log('createPetForm', createPetForm);

  Object.keys(createPetForm).forEach((key) => {
    // 使用 Reflect.has 替代 hasOwnProperty
    if (Reflect.has(form, key)) {
      // 显式类型断言，确保赋值安全
      (form as any)[key] = createPetForm[key];
    }
  });
};

// const PAGE_CONFIG = `PAGE_CONFIG_`

onMounted(() => {
  init();
});
</script>
<style lang="scss" scoped>
@import '../config/page.scss';
</style>
