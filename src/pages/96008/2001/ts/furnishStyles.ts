import { computed, reactive } from 'vue';

export const taskRequestInfo = reactive([
  {
    skuId: '',
    gradeValue: 0,
    id: '',
    level: '',
    gradeValueName: '',
    position: 1,
    giftSkuList: [],
    taskRule: '',
    sendTotalCount1: 1,
    joinSkuType: 1, // 1全店商品 2指定商品
    skuList: [],
    prizeList: [], // 根据实际情况，可能需要定义奖品的类型
  },
]);
export const furnish = reactive<{ [x: string]: string }>({
  pageBg: '',
  actBg: '', // 主页背景图
  actBgColor: '', // 主页背景色
  ruleBtn: '', // 活动规则按钮
  myPrizeBtn: '', // 我的奖品按钮
  seriesBg: '', // 系列背景图
  prizeBg: '', // 奖品背景图
});

const pageBg = computed(() => ({
  backgroundColor: furnish.actBgColor ?? '',
}));

const seriesBg = computed(() => ({
  backgroundImage: furnish.seriesBg ? `url("${furnish.seriesBg}")` : '',
}));

const seriesTitleColor = computed(() => ({
  color: furnish.seriesTitleColor ?? '',
}));

const bugAmountColor = computed(() => ({
  color: furnish.bugAmountColor ?? '',
}));

const stepBtnBg = computed(() => ({
  backgroundImage: furnish.stepBtnBg ? `url("${furnish.stepBtnBg}")` : '',
  color: furnish.stepTextColor ?? '',
}));

const stepBtnSelectBg = computed(() => ({
  backgroundImage: furnish.stepBtnSelectBg ? `url("${furnish.stepBtnSelectBg}")` : '',
  color: furnish.stepTextColor ?? '',
}));

const stepText = computed(() => ({
  fill: furnish.stepTextColor ?? '',
  stroke: furnish.stepTextColorShadow ?? '',
  strokeWidth: '0.06rem',
  paintOrder: 'stroke',
}));

const prizeBg = computed(() => ({
  backgroundImage: furnish.prizeBg ? `url("${furnish.prizeBg}")` : '',
}));

const skuListBg = computed(() => ({
  backgroundImage: furnish.skuListBg ? `url("${furnish.skuListBg}")` : '',
}));

const skuBg = computed(() => ({
  backgroundImage: furnish.skuBg ? `url("${furnish.skuBg}")` : '',
}));

const skuTitleBg = computed(() => ({
  backgroundImage: furnish.skuTitleBg ? `url("${furnish.skuTitleBg}")` : '',
  color: furnish.skuTitleColor ?? '',
}));

const userInfoBg = computed(() => ({
  backgroundImage: furnish.userInfoBg ? `url("${furnish.userInfoBg}")` : '',
  color: furnish.userInfoColor ?? '',
}));

const thresholdBg = computed(() => ({
  backgroundImage: furnish.thresholdBg ? `url("${furnish.thresholdBg}")` : '',
}));

const prizeListBg = computed(() => ({
  backgroundImage: furnish.prizeListBg ? `url("${furnish.prizeListBg}")` : '',
}));

const ruleBk = computed(() => ({
  backgroundImage: furnish.ruleBk ? `url("${furnish.ruleBk}")` : '',
}));

const ruleTextColor = computed(() => ({
  color: furnish.ruleTextColor ?? '',
}));

const joinMemberBk = computed(() => ({
  backgroundImage: furnish.joinMemberBk ? `url("${furnish.joinMemberBk}")` : '',
}));
const myPrizeBk = computed(() => ({
  backgroundImage: furnish.myPrizeBk ? `url("${furnish.myPrizeBk}")` : '',
}));
const confirmPrizeBk = computed(() => ({
  backgroundImage: furnish.confirmPrizeBk ? `url("${furnish.confirmPrizeBk}")` : '',
}));

const saveAddressBk = computed(() => ({
  backgroundImage: furnish.saveAddressBk ? `url("${furnish.saveAddressBk}")` : '',
}));

const receiveSuccessBk = computed(() => ({
  backgroundImage: furnish.receiveSuccessBk ? `url("${furnish.receiveSuccessBk}")` : '',
}));
const myPrizeTextColor = computed(() => ({
  color: furnish.myPrizeTextColor ?? '',
}));
export default {
  pageBg,
  seriesBg,
  seriesTitleColor,
  bugAmountColor,
  stepBtnBg,
  stepBtnSelectBg,
  stepText,
  prizeBg,
  skuListBg,
  skuBg,
  skuTitleBg,
  userInfoBg,
  thresholdBg,
  prizeListBg,

  ruleBk,
  ruleTextColor,
  joinMemberBk,
  myPrizeBk,
  myPrizeTextColor,
  confirmPrizeBk,
  saveAddressBk,
  receiveSuccessBk,
};
