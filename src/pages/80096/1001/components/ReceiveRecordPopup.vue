<template>
  <div>
    <CommonDrawer title="领奖记录" @close="emits('close')">
      <div class="h-[40vh] px-4 pt-2 leading-5 overflow-y-scroll text-[#33333] whitespace-pre-wrap text-xs">
        <div class="text-gray-400 text-center">活动奖励将于确认收货后进行发放。</div>
        <div v-if="prizeList.length" class="pb-2">
          <div class="bg-white rounded p-3 flex justify-between items-center mt-2" v-for="(item, index) in prizeList" :key="index">
            <div class="flex">
              <img class="w-10 h-10 mr-1" :src="item.prizeImg" alt="">
              <div>
                <div>奖品名称： {{ item.prizeName }}</div>
                <div>领取时间： {{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
              </div>
            </div>
            <!--实物&&已发放-->
            <div class="text-right" v-if="item.prizeType === ASSETS.PHYSICAL">
              <div class="text-yellow-400" v-if="!item.deliveryStatus && item.status!== 2">待发货</div>
              <div class="text-red-400" v-if="item.status === 2">已取消</div>
              <div class="text-green-400" v-if="item.deliveryStatus && item.status !==2">已发货</div>
              <div class="text-blue-400" v-if="!item.deliveryStatus && item.status !== 2 && !item.realName" @click="changAddress(item)">填写地址</div>
              <div class="text-blue-400" @click="changAddress(item)" v-else>查看地址</div>
            </div>
            <!--礼品卡&&已发放-->
            <div class="text-right" v-else-if="item.prizeType === ASSETS.GIFT_CARD && item.status === 3">
              <div class="text-green-400">{{ statusMap[item.status] }}</div>
              <div class="text-blue-400" @click="showCardNum(item)" v-if="item.status !== 2">立即兑换</div>
            </div>
            <!--(PLUS||AQY)&&已发放-->
            <div class="text-right" v-else-if="(item.prizeType === ASSETS.PLUS_MEMBER_CARD || item.prizeType === ASSETS.IQYI_MEMBER_CARD) && item.status === 3">
              <div class="text-green-400">{{ statusMap[item.status] }}</div>
              <div class="text-blue-400" @click="exchangePlusOrAiqiyi" v-if="item.status !== 2">立即兑换</div>
            </div>
            <!--京元宝&&已发放-->
            <div class="text-right" v-else-if="item.prizeType === ASSETS.JINGYUANBAO">
              <div class="text-yellow-400" :class="item.status === 2 && 'text-red-400'">{{ statusMap[item.status] }}</div>
              <div class="text-blue-400" @click="savePhone(item)" v-if="item.isFuLuWaitingReceive && item.status !== 2">点击领取</div>
            </div>
            <!--积分 京豆 红包 E卡 优惠券-->
            <!-- 直接发放 没有领奖过程 -->
            <div class="text-right" v-else>
              <div :class="item.status === 2 ? 'text-red-400' : 'text-green-400'">{{ statusMap[item.status] }}</div>
            </div>
          </div>
        </div>
        <div v-else class="text-gray-400 text-sm h-[80%] flex justify-center items-center">
          暂无领奖记录哦～
        </div>
      </div>
    </CommonDrawer>
    <VanPopup teleport="body" v-model:show="showSaveAddress" position="bottom">
      <SaveAddress :echoData="echoData" :userReceiveRecordId="userReceiveRecordId" :activityPrizeId="activityPrizeId" @close="showSaveAddress = false"></SaveAddress>
    </VanPopup>
  </div>
</template>
<script lang="ts" setup>
import CommonDrawer from '@/components/CommonDrawer/index.vue';
import { reactive, ref, inject } from 'vue';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import dayjs from 'dayjs';
import { exchangePlusOrAiqiyi } from '@/utils/platforms/jump';
import SaveAddress from './SaveAddress.vue';
import { isPreview } from '@/utils';
import { ASSETS } from '@/utils/enum';
import { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;

// 将数组翻译
// eslint-disable-next-line no-shadow
const emits = defineEmits(['close', 'showCardNum', 'savePhone']);
const statusMap = {
  1: '待发放',
  2: '已取消',
  3: '已发放',
};

interface Prize {
  prizeImg: string;
  prizeName: string;
  createTime: string;
  status: number;
  prizeType: number;
  deliveryStatus: number;
  prizeContent: string;
  isFuLuWaitingReceive: boolean;
  userReceiveRecordId: string;
}

const prizeList = ref<Prize[]>([]);

const showSaveAddress = ref(false);
const userReceiveRecordId = ref('');
const activityPrizeId = ref('');

const echoData: any = reactive({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
});
const getRecord = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/80096/getReceive');
    prizeList.value = data;
    closeToast();
  } catch (error: any) {
    closeToast();
  }
};

// 修改地址
const changAddress = (item: any) => {
  if (baseInfo.status === 3 && !item.realName) {
    showToast('活动已结束~');
    return;
  }
  userReceiveRecordId.value = item.userReceiveRecordId;
  activityPrizeId.value = item.activityPrizeId;
  Object.keys(echoData).forEach((key) => {
    echoData[key] = item[key];
  });
  showSaveAddress.value = true;
};

// 关闭收货地址
const closeSaveAddress = (type: boolean | undefined) => {
  showSaveAddress.value = false;
  if (type) {
    setTimeout(() => {
      getRecord();
    }, 1000);
  }
};

// 展示礼品卡
const showCardNum = (item: Prize) => {
  const prizeContent = JSON.parse(item.prizeContent);
  const { prizeName, prizeImg } = item;
  emits('showCardNum', { ...prizeContent, prizeName, showImg: prizeImg });
};

// 领取京元宝
const savePhone = (item: Prize) => {
  const prizeContent = item.prizeContent ? JSON.parse(item.prizeContent) : { result: { planDesc: '-' } };
  emits('savePhone', item.userReceiveRecordId, prizeContent.result.planDesc);
};

!isPreview && getRecord();
</script>
