// sku 接口类型
export interface Skus {
  name?: string;
}

// res 接口类型
export interface Res {
  data?: any,
  code?: number,
  message?: string
}

// api 接口类型
export interface ApisOpts {
  receivePrize: string,
  getPrizeRecord: string,
  activityContent: string,

  [propname: string]: any,
}

export interface IGift {
  status: number;
  prizeName: string;
  img: string;
  rightsId: string;
  prizeImg: string;
  price: number;
}

export interface ISku {
  image: string;
  skuId: string;
}

export interface IActivityData {
  activityDesc: string,
  activityName: string,
  remark: string,
  rules: string,
  firstBuy: boolean,
  isMember: boolean,
  button: IGift[],
  skuList: ISku[],
}
