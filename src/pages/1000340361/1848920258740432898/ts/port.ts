import { closeToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import type { BaseInfo } from '@/types/BaseInfo';
import { ApisOpts, Res } from './type';

// 获取BaseInfo
let baseInfo: BaseInfo;
export const setBaseInfo = (info: BaseInfo) => {
  baseInfo = info;
};

// api 接口链接
const apis: ApisOpts = {
  receivePrize: '/sanofi/upgift/1848920258740432898/receivePrize',
  getPrizeRecord: '/sanofi/upgift/1848920258740432898/getPrizeRecord',
  activityContent: '/sanofi/upgift/1848920258740432898/loadMainPage',
};

// 活动通用接口 - POST || GET
export const getDataInterface = async (apiName: string, args?: any) => {
  let res: Res = {};
  // 默认传入 venderId activityId
  const { shopId, activityMainId } = baseInfo;
  // 获取其他入参
  const data = { ...args ?? {} };
  try {
    res = await httpRequest.post(apis[apiName], { shopId, activityMainId, ...data });
    console.log(res);
    closeToast();
    if (res.code === 200) {
      return { result: true, data: res.data, message: '' };
    }
    if (res.message) showToast(res.message);
    return { result: false, data: {}, message: res.message };
  } catch (err) {
    showToast(err.message);
    return { result: false, data: {}, message: res.message };
  }
};

export default {
  getDataInterface,
};
