<template>
  <!-- 确认领取弹窗 -->
  <div class='box'>
    <div class='tip-message'>您确认领取<br />{{ giftInfo.prizeName }}奖品吗？</div>
    <div class='sure-btn' @click='sureDraw()'></div>
  </div>
</template>

<script lang='ts' setup>
import { defineProps, defineEmits, PropType } from 'vue';
import { IGift } from '../ts/type';

const props = defineProps({ giftInfo: Object as PropType<IGift> });
const emits = defineEmits(['sureDraw']);

const sureDraw = () => {
  emits('sureDraw');
};
</script>

<style lang='scss' scoped>
.box {
  width: 5rem;
  height: 6.7rem;
  background-image: url("../assets/img/sure-draw-dialog.png");

  .tip-message {
    width: 5rem;
    height: 1.2rem;
    text-align: center;
    position: absolute;
    font-size: .35rem;
    font-weight: bold;
    font-family: SourceHanSansCNB;
    color: #885334;
    top: .6rem;
    left: 50%;
    transform: translateX(-50%);
  }

  .sure-btn {
    width: 3rem;
    height: 1rem;
    position: absolute;
    bottom: .2rem;
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>
