<template>
  <!-- 活动规则弹窗 -->
  <div class='box'>
    <!-- 规则 -->
    <div class='rule-view'>
      <div class='rule' v-html='rule'></div>
    </div>

  </div>
</template>

<script lang='ts' setup>
import { defineEmits, defineProps, ref } from 'vue';

const props = defineProps({ rule: String });

</script>

<style lang='scss' scoped>
.box {
  background-image: url("../assets/img/rule-dialog.png");

  .rule-view {
    margin: 0 auto;
    position: absolute;
    top: 1.5rem;
    width: 100%;
    height: 5.5rem;
    padding: 0.4rem 0.5rem;
    box-sizing: border-box;

    .rule {
      width: 100%;
      height: 4.5rem;
      padding: 0 .1rem;
      overflow-y: auto;
      font-size: .26rem;
      text-align: left;
      white-space: break-spaces;
    }
  }
}
</style>
