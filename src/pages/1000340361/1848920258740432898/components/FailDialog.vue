<template>
  <!-- 不满足条件 -->
  <div class='box' @click='jumpPage()'>
    <div class='count-down-view' v-if='countdown>0'>{{ countdown }}s</div>
  </div>
</template>

<script lang='ts' setup>
import { onMounted, ref } from 'vue';

const countdown = ref(3);
let intervalId: number | undefined;

const jumpPage = () => {
  window.location.href = 'https://mall.jd.com/index-1000340361.html';
};

const stopCountdown = () => {
  if (intervalId) {
    clearInterval(intervalId);
    window.location.href = 'https://mall.jd.com/index-1000340361.html';
  }
};

const startCountdown = () => {
  intervalId = setInterval(() => {
    if (countdown.value > 0) {
      countdown.value--;
    } else {
      stopCountdown();
    }
  }, 1000);
};

onMounted(() => {
  startCountdown();
});
</script>

<style lang='scss' scoped>
.box {
  width: 6.23rem;
  height: 6.83rem;
  background-image: url("../assets/img/fail-dialog.png");

  .count-down-view {
    position: absolute;
    right: 1.2rem;
    top: 0.4rem;
    font-size: 0.5rem;
    background: linear-gradient(90deg, #ecd7a9, #f0ddb5, #f7eacd, #fdf5e2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
  }
}
</style>
