<template>
  <!-- 我的奖品弹窗 -->
  <div class='box'>

    <div class='my-prize-view' v-if='myPrizeList.length>0'>
      <div class='my-prize-item' v-for='(item,index) in myPrizeList' :key='index'>
        <div class='row-view'>{{ item.rightsName }}</div>
        <div class='row-view'>{{ dayjs(item.createTime).format('YYYY-MM-DD') }}</div>
      </div>
    </div>
    <div class='none-data-tip' v-else>
      暂无数据
    </div>
  </div>
</template>

<script lang='ts' setup>
import { defineProps, PropType } from 'vue';
import dayjs from 'dayjs';

const props = defineProps({ myPrizeList: Array as PropType<any[]> });
</script>

<style lang='scss' scoped>
.box {
  width: 5.6rem;
  height: 6.7rem;
  padding: 3rem 0.3rem 0.4rem;
  background-image: url("../assets/img/my-prize-dialog.png");

  .my-prize-view {
    height: 3rem;
    overflow-y: auto;

    .my-prize-item {
      font-size: .3rem;
      margin-bottom: .2rem;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      line-height: .6rem;
      font-family: SourceHanSansCNB;

      .row-view {
        width: 40%;
      }
    }
  }
}
</style>
