<!--
 * @actName:
 * @author: <PERSON><PERSON><PERSON> lin
-->
<template>
  <!-- background -->
  <div class='background'>

    <div class='sider-btn' @click='openDialog("ruleDialog")'>活动规则</div>
    <div class='sider-btn' style='top:3rem' @click='getMyPrize()'>我的奖品</div>
    <div class='sider-btn' style='top:3.7rem' @click='shareAct()'>分享活动</div>

    <div class='title-view'>活动攻略</div>
    <div class='act-strategy-view'>
      <div v-html='activityData.remark' class='strategy-tip'></div>
      <div class='act-view'>
        <div class='act-item' v-for='(item,index) in activityData.button' :key='index'>
          <div class='act-price'>活动{{ index + 1 }}: 满{{ item.price }}元</div>
          <div class='act-prize'>
            赠{{ item.prizeName }}
            <img :src='item.img' style='width: .9rem;margin-left: .1rem' alt=''>
          </div>
        </div>
      </div>
      <div v-html='activityData.activityDesc' style='margin-top: .4rem;font-size: .22rem;text-align: center'></div>
    </div>

    <div class='title-view'>奖品池</div>
    <div class='act-strategy-view'>
      <div class='gift-view'>
        <div class='gift-item' v-for='(item,index) in activityData.button' :key='index'>
          <div class='gift-name'>{{ item.prizeName }}</div>
          <img :src='item.img' style='width: 2.4rem' alt=''>
          <div class='gift-draw-btn' @click='drawGift(item)' :class='{"gray":item.status!==1}'>{{ getDrawGiftBtn(item.status) }}</div>
        </div>
      </div>
    </div>

    <div class='title-view'>参与活动奖品</div>
    <div class='sku-view'>
      <img :src='item.image' @click='gotoSkuPage(item.skuId)' alt='' v-for='(item,index) in activityData.skuList' style='width: 3.7rem' :key='index'>
    </div>

  </div>

  <Popup class='popup' v-model:show='dialogShow' :close-on-click-overlay='getCloseOverlay()'>
    <RuleDialog :rule='activityData.rules' v-if="dialogName === 'ruleDialog'"></RuleDialog>
    <NoMemberDialog v-if="dialogName === 'noMemberDialog'"></NoMemberDialog>
    <SureDrawDialog v-if="dialogName === 'sureDrawDialog'" :giftInfo='currentGift' @sureDraw='sureDraw'></SureDrawDialog>
    <FailDialog v-if="dialogName === 'failDialog'"></FailDialog>
    <HasDrawDialog v-if="dialogName === 'hasDrawDialog'"></HasDrawDialog>
    <SuccessDialog v-if="dialogName === 'successDialog'" :drawGiftImgBg='currentGift.value.prizeImg'></SuccessDialog>
    <MyPrizeDialog v-if="dialogName === 'myPrizeDialog'" :myPrizeList='myPrizeList'></MyPrizeDialog>
  </Popup>

</template>

<script lang='ts' setup>
import { ref, inject, Ref, onMounted, reactive } from 'vue';
import { Toast, Popup } from 'vant';
import { BaseInfo } from '@/types/BaseInfo';
import { callShare } from '@/utils/platforms/share';
import type { IActivityData, IGift } from './ts/type';
import { gotoSkuPage } from '@/utils/platforms/jump';

/* ---------------------------------  弹窗  ------------------------------ */
import RuleDialog from './components/RuleDialog.vue';
import FailDialog from './components/FailDialog.vue';
import SuccessDialog from './components/SuccessDialog.vue';
import MyPrizeDialog from './components/MyPrizeDialog.vue';
import HasDrawDialog from './components/HasDrawDialog.vue';
import NoMemberDialog from './components/NoMemberDialog.vue';
import SureDrawDialog from './components/SureDrawDialog.vue';
import { openDialog, closeDialog, dialogName, dialogShow } from './ts/dialog';
/* ---------------------------------  接口  ------------------------------- */
import { getDataInterface, setBaseInfo } from './ts/port';

const baseInfo: BaseInfo = inject('baseInfo') as BaseInfo;
setBaseInfo(baseInfo);

const shareConfig = JSON.parse(window.sessionStorage.getItem('LZ_SHARE_CONFIG') ?? '');
const shareAct = () => {
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
  });
};

const getCloseOverlay = () => !(dialogName.value === 'noMemberDialog' || dialogName.value === 'failDialog');

const myPrizeList = ref([]);
const getMyPrize = async () => {
  const data = await getDataInterface('getPrizeRecord');
  if (data.result) {
    myPrizeList.value = data.data ?? [];
    openDialog('myPrizeDialog');
  }
};

// 主接口
const activityData = reactive({}) as IActivityData;
const activityContent = async () => {
  const data = await getDataInterface('activityContent');
  if (data.result) {
    Object.assign(activityData, data.data);
    if (!activityData.firstBuy) {
      openDialog('failDialog');
    }
  }
};

const currentGift = ref({});
const drawGift = (giftItem: IGift) => {
  currentGift.value = giftItem;
  openDialog('sureDrawDialog');
  if (!activityData.isMember) {
    openDialog('noMemberDialog');
  } else if (giftItem.status === 1) {
    currentGift.value = giftItem;
    openDialog('sureDrawDialog');
  } else if (giftItem.status === 2) {
    openDialog('hasDrawDialog');
  }
};

const sureDraw = async () => {
  const data = await getDataInterface('receivePrize', { rightsId: currentGift.value.rightsId });
  if (data.result) {
    openDialog('successDialog');
  }
};

const getDrawGiftBtn = (status: number) => {
  switch (status) {
    case 0:
      return '不满足条件';
    case 2:
      return '已领取';
    case 3:
      return '已领完';
    default:
      return '立即领取>';
  }
};

activityContent();

</script>

<style lang='scss'>
.gray {
  /*grayscale(val):val值越大灰度就越深*/
  -webkit-filter: grayscale(100%) brightness(1);
  -moz-filter: grayscale(100%) brightness(1);
  -ms-filter: grayscale(100%) brightness(1);
  -o-filter: grayscale(100%) brightness(1);
  filter: grayscale(100%) brightness(1);
  filter: gray brightness;
}

#app .van-popup {
  background-color: transparent;
}
</style>
