@font-face {
  font-family: 'OPPOSansM';
  src: url('https://lzcdn.dianpusoft.cn/fonts/OPPOSans/OPPOSans-M.woff2') format('woff2'),
  url('https://lzcdn.dianpusoft.cn/fonts/OPPOSans/OPPOSans-M.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'OPPOSansB';
  src: url('https://lzcdn.dianpusoft.cn/fonts/OPPOSans/OPPOSans-B.woff2') format('woff2'),
  url('https://lzcdn.dianpusoft.cn/fonts/OPPOSans/OPPOSans-B.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SourceHanSansCNB';
  src: url('https://lzcdn.dianpusoft.cn/fonts/SourceHanSansCN/SourceHanSansCN-Bold.otf') format('otf');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

* {
  box-sizing: border-box;
}

.gray {
  filter: grayscale(1);
}

html {
  max-width: 750px;
  margin: 0 auto;
  background: #fcf9ee;

  .background {
    min-height: 100vh;
    position: relative;
    padding-top: 14.26rem;
    margin-bottom: 1rem;
    box-sizing: border-box;
    background: #fcf9ee url("../assets/img/background.jpg") no-repeat;
    background-size: contain;

    .sider-btn {
      width: 1.36rem;
      height: .48rem;
      text-align: center;
      padding-left: .1rem;
      font-size: .3rem;
      font-family: SourceHanSansCNB;
      position: absolute;
      right: 0;
      top: 2.3rem;
      background: {
        image: url("../assets/img/sider-btn.png");
        repeat: no-repeat;
        size: contain;
      };
    }

    .title-view {
      width: 7.5rem;
      height: 2rem;
      font-size: .7rem;
      line-height: 1.75rem;
      margin-top: .3rem;
      text-align: center;
      color: #323030;
      background: {
        image: url("../assets/img/title-bg.png");
        repeat: no-repeat;
        size: contain;
      };
    }

    .act-strategy-view {
      width: 7.5rem;
      height: 5.13rem;
      padding: .7rem 0 .2rem;
      background: {
        image: url("../assets/img/act-view-bg.png");
        repeat: no-repeat;
        size: contain;
      };

      .strategy-tip {
        font-size: .28rem;
        text-align: center;
        font-family: OPPOSansM;
      }

      .act-view {
        margin-top: .4rem;

        .act-item {
          width: 6.05rem;
          height: .77rem;
          margin: 0 auto .2rem;
          padding: 0 .2rem 0 .4rem;
          display: flex;
          align-items: center;
          justify-content: space-between;
          background: {
            image: url("../assets/img/act-item-bg.png");
            repeat: no-repeat;
            size: contain;
          };

          .act-price {
            font-family: OPPOSansB;
            font-size: .32rem;
            letter-spacing: -.01rem;
          }

          .act-prize {
            display: flex;
            align-items: center;
            font-weight: bold;
            font-size: .36rem;
            background: linear-gradient(90deg, #ecd7a9, #f0ddb5, #f7eacd, #fdf5e2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
          }
        }
      }

      .gift-view {
        display: flex;
        align-items: center;
        justify-content: center;

        .gift-item {
          width: 3.65rem;
          height: 4rem;
          padding: .2rem 0 .3rem;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: space-evenly;
          background: {
            image: url("../assets/img/gift-view-bg.png");
            repeat: no-repeat;
            size: contain;
          };

          .gift-name {
            font-size: .3rem;
            text-align: center;
            font-family: OPPOSansB;
          }

          .gift-draw-btn {
            width: 2rem;
            height: .51rem;
            line-height: .55rem;
            font-family: OPPOSansB;
            text-align: center;
            background: {
              image: url("../assets/img/gift-draw-btn.png");
              repeat: no-repeat;
              size: contain;
            };
          }
        }
      }
    }

    .sku-view {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
    }
  }
}

.box {
  width: 6rem;
  height: 7.21rem;
  position: relative;
  background: {
    repeat: no-repeat;
    size: contain;
  };
}

.none-data-tip {
  text-align: center;
  font-size: .33rem;
  color: #222222;
  line-height: 1.2rem;
}
