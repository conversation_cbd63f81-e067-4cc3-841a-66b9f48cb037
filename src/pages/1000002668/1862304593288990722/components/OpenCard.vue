<template>
  <popup teleport="body" v-model:show="isShowPopup" :close-on-click-overlay="false">
    <div class="box">
      <div class="dialog" @click="goOpenCard" :style="{ backgroundImage: `url(${bg})` }">
        <div class="btn"></div>
      </div>
    </div>
  </popup>
</template>

<script setup lang="ts">
import { computed, defineEmits, defineProps, inject, ref, PropType } from 'vue';
import { Popup } from 'vant';
import type { BaseInfo } from '@/types/BaseInfo';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps({
  showPopup: {
    type: Boolean,
    required: true,
    default: false,
  },
  bg: {
    type: String,
    required: true,
    default: '',
  },
});
const isShowPopup = computed(() => props.showPopup);
const emits = defineEmits(['closeDialog']);
const goOpenCard = () => {
  window.location.href = `${baseInfo.openCardLink}&returnUrl=${encodeURIComponent(`${window.location.href}`)}`;
};
</script>
<style lang="scss" scoped>
.box {
  .dialog {
    background: url(https://img10.360buyimg.com/imgzone/jfs/t1/195516/35/55300/70106/674eabb3F92937277/1a2463fdd2248327.png) no-repeat center center;
    background-size: contain;
    width: 6.5rem;
    height: 8rem;
    box-sizing: border-box;
    padding: 1.8rem 0.85rem;
    position: relative;
    .title {
      font-size: 0.56rem;
      text-align: center;
      color: #1b3f7d;
    }

    .dialog_rule {
      max-height: 5.6rem;
      overflow-y: auto;
      font-size: 0.32rem;
      font-weight: normal;
      letter-spacing: 0.01rem;
      color: #1b3f7d;
      margin-top: 0.1rem;
      text-align: left;
    }
    .btn {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      height: 2rem;
    }
  }
}
</style>
