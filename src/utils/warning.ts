import Axios from 'axios';

const warningRequest = Axios.create({
  baseURL: process.env.VUE_APP_API_HOST,
  timeout: 60 * 1000,
  headers: {
    'Content-Type': 'application/json; charset=UTF-8',
  },
});

/**
 * 格式化性能指标数据
 * @returns {string} 格式化后的性能指标字符串
 */
const formatPerformanceMetrics = (): string => {
  try {
    const [entry] = performance.getEntriesByType('navigation') as PerformanceNavigationTiming[];
    if (!entry) {
      return '性能指标获取失败';
    }
    const metrics = {
      // DNS解析时间
      dnsTime: entry.domainLookupEnd - entry.domainLookupStart,
      // TCP连接时间
      tcpTime: entry.connectEnd - entry.connectStart,
      // 首字节时间
      ttfb: entry.responseStart,
      // DOM解析时间
      domParsingTime: entry.domComplete - entry.domInteractive,
      // 页面完全加载时间
      loadTime: entry.loadEventEnd,
      // 白屏时间
      whiteScreenTime: entry.domInteractive,
      // DOM Ready时间
      domReadyTime: entry.domContentLoadedEventEnd,
    };
    return `首字节时间(TTFB): ${metrics.ttfb.toFixed(2)}ms;DOM解析耗时: ${metrics.domParsingTime.toFixed(2)}ms;页面加载总耗时: ${metrics.loadTime.toFixed(2)}ms;白屏时间: ${metrics.whiteScreenTime.toFixed(2)}ms;DOM Ready时间: ${metrics.domReadyTime.toFixed(2)}ms`;
  } catch (e) {
    return '性能指标获取失败';
  }
};

/**
 * 发送告警信息
 */
export const sendWarning = async (name: string, message: string, apiUrl?: string) => {
  await warningRequest.post('performance/warning', {
    name,
    message,
    apiUrl,
    pageUrl: window.location.href,
    createTime: new Date(),
    userName: sessionStorage.getItem('LZ_JD_USER_NAME'),
    userPin: sessionStorage.getItem('LZ_JD_ENCRYPT_PIN'),
    agent: navigator.userAgent,
    shopId: sessionStorage.getItem('LZ_SHOP_ID'),
    activityId: sessionStorage.getItem('LZ_ACTIVITY_ID'),
    performance: formatPerformanceMetrics(),
  });
};
